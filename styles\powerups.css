/* Power-up Styles */

/* Super Mushroom */
.super-mushroom {
    position: absolute;
    width: 32px;
    height: 32px;
    z-index: 35;
}

.super-mushroom .mushroom-sprite {
    width: 32px;
    height: 32px;
    background: 
        /* Cap */
        radial-gradient(ellipse 16px 12px at 16px 12px, #FF0000 0%, #CC0000 70%, transparent 70%),
        /* Stem */
        radial-gradient(ellipse 8px 16px at 16px 24px, #FFFF99 0%, #FFFF77 70%, transparent 70%),
        /* Spots */
        radial-gradient(circle 3px at 10px 8px, #FFFFFF 0%, #FFFFFF 100%, transparent 100%),
        radial-gradient(circle 3px at 22px 10px, #FFFFFF 0%, #FFFFFF 100%, transparent 100%),
        radial-gradient(circle 2px at 16px 6px, #FFFFFF 0%, #FFFFFF 100%, transparent 100%);
    position: relative;
    animation: mushroomGlow 2s ease-in-out infinite;
}

@keyframes mushroomGlow {
    0%, 100% { 
        box-shadow: 0 0 5px rgba(255, 0, 0, 0.5);
        transform: translateY(0);
    }
    50% { 
        box-shadow: 0 0 15px rgba(255, 0, 0, 0.8);
        transform: translateY(-2px);
    }
}

/* Fire Flower */
.fire-flower {
    position: absolute;
    width: 32px;
    height: 32px;
    z-index: 35;
}

.fire-flower .flower-sprite {
    width: 32px;
    height: 32px;
    background: 
        /* Stem */
        linear-gradient(to bottom, #228B22 0%, #006400 100%),
        /* Petals */
        radial-gradient(circle 6px at 16px 8px, #FF4500 0%, #FF0000 100%),
        radial-gradient(circle 6px at 8px 16px, #FF4500 0%, #FF0000 100%),
        radial-gradient(circle 6px at 24px 16px, #FF4500 0%, #FF0000 100%),
        radial-gradient(circle 6px at 16px 24px, #FF4500 0%, #FF0000 100%),
        /* Center */
        radial-gradient(circle 4px at 16px 16px, #FFFF00 0%, #FFA500 100%);
    position: relative;
    animation: flowerPulse 1.5s ease-in-out infinite;
}

@keyframes flowerPulse {
    0%, 100% { 
        transform: scale(1) rotate(0deg);
        box-shadow: 0 0 10px rgba(255, 69, 0, 0.6);
    }
    50% { 
        transform: scale(1.1) rotate(5deg);
        box-shadow: 0 0 20px rgba(255, 69, 0, 0.9);
    }
}

/* Star Power */
.star-power {
    position: absolute;
    width: 32px;
    height: 32px;
    z-index: 35;
}

.star-power .star-sprite {
    width: 32px;
    height: 32px;
    background: 
        conic-gradient(from 0deg, #FFD700 0deg, #FF69B4 72deg, #00BFFF 144deg, #32CD32 216deg, #FF4500 288deg, #FFD700 360deg);
    clip-path: polygon(50% 0%, 61% 35%, 98% 35%, 68% 57%, 79% 91%, 50% 70%, 21% 91%, 32% 57%, 2% 35%, 39% 35%);
    position: relative;
    animation: starSpin 1s linear infinite;
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
}

@keyframes starSpin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* 1-Up Mushroom */
.oneup-mushroom {
    position: absolute;
    width: 32px;
    height: 32px;
    z-index: 35;
}

.oneup-mushroom .mushroom-sprite {
    width: 32px;
    height: 32px;
    background: 
        /* Cap */
        radial-gradient(ellipse 16px 12px at 16px 12px, #00AA00 0%, #008800 70%, transparent 70%),
        /* Stem */
        radial-gradient(ellipse 8px 16px at 16px 24px, #FFFF99 0%, #FFFF77 70%, transparent 70%),
        /* Spots */
        radial-gradient(circle 3px at 10px 8px, #FFFFFF 0%, #FFFFFF 100%, transparent 100%),
        radial-gradient(circle 3px at 22px 10px, #FFFFFF 0%, #FFFFFF 100%, transparent 100%),
        radial-gradient(circle 2px at 16px 6px, #FFFFFF 0%, #FFFFFF 100%, transparent 100%);
    position: relative;
    animation: oneupFloat 2s ease-in-out infinite;
}

@keyframes oneupFloat {
    0%, 100% { 
        transform: translateY(0);
        box-shadow: 0 0 10px rgba(0, 170, 0, 0.6);
    }
    50% { 
        transform: translateY(-5px);
        box-shadow: 0 0 20px rgba(0, 170, 0, 0.9);
    }
}

/* Cape Feather */
.cape-feather {
    position: absolute;
    width: 32px;
    height: 32px;
    z-index: 35;
}

.cape-feather .feather-sprite {
    width: 32px;
    height: 32px;
    background: 
        /* Feather shaft */
        linear-gradient(to bottom, #8B4513 0%, #654321 100%),
        /* Feather vanes */
        radial-gradient(ellipse 12px 20px at 8px 16px, #32CD32 0%, #228B22 70%, transparent 70%),
        radial-gradient(ellipse 12px 20px at 24px 16px, #32CD32 0%, #228B22 70%, transparent 70%);
    position: relative;
    animation: featherSway 3s ease-in-out infinite;
}

@keyframes featherSway {
    0%, 100% { 
        transform: rotate(-5deg);
        box-shadow: 0 0 8px rgba(50, 205, 50, 0.5);
    }
    50% { 
        transform: rotate(5deg);
        box-shadow: 0 0 15px rgba(50, 205, 50, 0.8);
    }
}

/* Power-up Collection Effect */
.powerup.collecting {
    animation: powerupCollect 0.8s ease-out forwards;
}

@keyframes powerupCollect {
    0% { 
        transform: scale(1) translateY(0);
        opacity: 1;
    }
    50% { 
        transform: scale(1.3) translateY(-20px);
        opacity: 0.9;
    }
    100% { 
        transform: scale(0) translateY(-50px);
        opacity: 0;
    }
}

/* Power-up Spawn Effect */
.powerup.spawning {
    animation: powerupSpawn 1s ease-out;
}

@keyframes powerupSpawn {
    0% { 
        transform: scale(0) translateY(-20px);
        opacity: 0;
    }
    50% { 
        transform: scale(1.2) translateY(-10px);
        opacity: 0.8;
    }
    100% { 
        transform: scale(1) translateY(0);
        opacity: 1;
    }
}

/* Power-up Movement */
.powerup.moving {
    animation: powerupMove 0.5s ease-in-out infinite alternate;
}

@keyframes powerupMove {
    from { transform: translateX(0); }
    to { transform: translateX(2px); }
}

/* Mini Mushroom */
.mini-mushroom {
    position: absolute;
    width: 24px;
    height: 24px;
    z-index: 35;
}

.mini-mushroom .mushroom-sprite {
    width: 24px;
    height: 24px;
    background: 
        /* Cap */
        radial-gradient(ellipse 12px 9px at 12px 9px, #0066FF 0%, #0044CC 70%, transparent 70%),
        /* Stem */
        radial-gradient(ellipse 6px 12px at 12px 18px, #FFFF99 0%, #FFFF77 70%, transparent 70%),
        /* Spots */
        radial-gradient(circle 2px at 8px 6px, #FFFFFF 0%, #FFFFFF 100%, transparent 100%),
        radial-gradient(circle 2px at 16px 7px, #FFFFFF 0%, #FFFFFF 100%, transparent 100%);
    position: relative;
    animation: miniFloat 1.5s ease-in-out infinite;
}

@keyframes miniFloat {
    0%, 100% { 
        transform: translateY(0) scale(1);
        box-shadow: 0 0 5px rgba(0, 102, 255, 0.5);
    }
    50% { 
        transform: translateY(-3px) scale(1.05);
        box-shadow: 0 0 12px rgba(0, 102, 255, 0.8);
    }
}

/* Mega Mushroom */
.mega-mushroom {
    position: absolute;
    width: 48px;
    height: 48px;
    z-index: 35;
}

.mega-mushroom .mushroom-sprite {
    width: 48px;
    height: 48px;
    background: 
        /* Cap */
        radial-gradient(ellipse 24px 18px at 24px 18px, #FF8C00 0%, #FF4500 70%, transparent 70%),
        /* Stem */
        radial-gradient(ellipse 12px 24px at 24px 36px, #FFFF99 0%, #FFFF77 70%, transparent 70%),
        /* Spots */
        radial-gradient(circle 4px at 15px 12px, #FFFFFF 0%, #FFFFFF 100%, transparent 100%),
        radial-gradient(circle 4px at 33px 15px, #FFFFFF 0%, #FFFFFF 100%, transparent 100%),
        radial-gradient(circle 3px at 24px 9px, #FFFFFF 0%, #FFFFFF 100%, transparent 100%);
    position: relative;
    animation: megaPulse 1s ease-in-out infinite;
}

@keyframes megaPulse {
    0%, 100% { 
        transform: scale(1);
        box-shadow: 0 0 15px rgba(255, 140, 0, 0.7);
    }
    50% { 
        transform: scale(1.1);
        box-shadow: 0 0 25px rgba(255, 140, 0, 1);
    }
}
