/* Background Styles */

/* Level 1 - Grassland */
.background.level-1 {
    background: 
        /* Sky gradient */
        linear-gradient(to bottom, #87CEEB 0%, #87CEEB 70%, #90EE90 70%, #90EE90 100%),
        /* Clouds */
        radial-gradient(ellipse 60px 30px at 200px 100px, rgba(255, 255, 255, 0.8) 0%, transparent 70%),
        radial-gradient(ellipse 80px 40px at 500px 80px, rgba(255, 255, 255, 0.7) 0%, transparent 70%),
        radial-gradient(ellipse 50px 25px at 800px 120px, rgba(255, 255, 255, 0.8) 0%, transparent 70%),
        radial-gradient(ellipse 70px 35px at 1100px 90px, rgba(255, 255, 255, 0.7) 0%, transparent 70%),
        /* Hills */
        radial-gradient(ellipse 200px 100px at 300px 100%, #32CD32 0%, transparent 70%),
        radial-gradient(ellipse 150px 75px at 700px 100%, #228B22 0%, transparent 70%),
        radial-gradient(ellipse 180px 90px at 1000px 100%, #32CD32 0%, transparent 70%);
    animation: cloudsMove 20s linear infinite;
}

@keyframes cloudsMove {
    from { background-position: 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px; }
    to { background-position: -100px 0px, -80px 0px, -60px 0px, -90px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px; }
}

/* Level 2 - Underground */
.background.level-2 {
    background: 
        /* Dark cave background */
        linear-gradient(to bottom, #2F2F2F 0%, #1C1C1C 50%, #000000 100%),
        /* Stalactites */
        linear-gradient(180deg, #696969 0%, transparent 30%),
        /* Cave walls texture */
        repeating-linear-gradient(
            45deg,
            rgba(105, 105, 105, 0.1) 0px,
            rgba(105, 105, 105, 0.1) 2px,
            transparent 2px,
            transparent 10px
        );
    position: relative;
}

.background.level-2::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        /* Glowing crystals */
        radial-gradient(circle 10px at 150px 200px, #00FFFF 0%, transparent 70%),
        radial-gradient(circle 8px at 400px 150px, #FF00FF 0%, transparent 70%),
        radial-gradient(circle 12px at 650px 250px, #00FF00 0%, transparent 70%),
        radial-gradient(circle 6px at 900px 180px, #FFFF00 0%, transparent 70%);
    animation: crystalGlow 3s ease-in-out infinite alternate;
}

@keyframes crystalGlow {
    from { opacity: 0.3; }
    to { opacity: 0.8; }
}

/* Level 3 - Water Level */
.background.level-3 {
    background: 
        /* Water gradient */
        linear-gradient(to bottom, #4169E1 0%, #1E90FF 30%, #00BFFF 60%, #87CEEB 100%),
        /* Water bubbles */
        radial-gradient(circle 5px at 100px 400px, rgba(255, 255, 255, 0.6) 0%, transparent 70%),
        radial-gradient(circle 3px at 250px 350px, rgba(255, 255, 255, 0.5) 0%, transparent 70%),
        radial-gradient(circle 7px at 400px 450px, rgba(255, 255, 255, 0.4) 0%, transparent 70%),
        radial-gradient(circle 4px at 600px 380px, rgba(255, 255, 255, 0.6) 0%, transparent 70%),
        radial-gradient(circle 6px at 800px 420px, rgba(255, 255, 255, 0.5) 0%, transparent 70%),
        /* Seaweed */
        linear-gradient(to top, #228B22 0%, transparent 40%);
    animation: waterFlow 8s ease-in-out infinite, bubbleFloat 5s linear infinite;
}

@keyframes waterFlow {
    0%, 100% { filter: hue-rotate(0deg); }
    50% { filter: hue-rotate(10deg); }
}

@keyframes bubbleFloat {
    from { background-position: 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px, 0px 0px; }
    to { background-position: 10px -50px, -5px -80px, 15px -60px, -10px -70px, 8px -90px, 0px 0px; }
}

/* Level 4 - Castle */
.background.level-4 {
    background: 
        /* Dark stormy sky */
        linear-gradient(to bottom, #2F2F2F 0%, #4B0082 50%, #8B0000 100%),
        /* Lightning effect */
        radial-gradient(ellipse 200px 400px at 300px 0px, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        /* Castle walls */
        repeating-linear-gradient(
            0deg,
            #696969 0px,
            #696969 20px,
            #2F4F4F 20px,
            #2F4F4F 40px
        ),
        /* Lava glow */
        radial-gradient(ellipse 100% 50px at 50% 100%, #FF4500 0%, transparent 70%);
    animation: lightning 4s ease-in-out infinite, lavaGlow 2s ease-in-out infinite alternate;
}

@keyframes lightning {
    0%, 90%, 100% { filter: brightness(1); }
    5%, 10% { filter: brightness(2); }
}

@keyframes lavaGlow {
    from { background-position: 0px 0px, 0px 0px, 0px 0px, 0px 0px; }
    to { background-position: 0px 0px, 0px 0px, 0px 0px, 0px 5px; }
}

/* Level 5 - Sky Level */
.background.level-5 {
    background: 
        /* Sky gradient */
        linear-gradient(to bottom, #FF69B4 0%, #FFB6C1 30%, #87CEEB 60%, #E0E0E0 100%),
        /* Rainbow */
        linear-gradient(90deg, 
            transparent 0%, 
            #FF0000 10%, 
            #FF7F00 20%, 
            #FFFF00 30%, 
            #00FF00 40%, 
            #0000FF 50%, 
            #4B0082 60%, 
            #9400D3 70%, 
            transparent 80%
        ),
        /* Floating clouds */
        radial-gradient(ellipse 100px 50px at 150px 200px, rgba(255, 255, 255, 0.9) 0%, transparent 70%),
        radial-gradient(ellipse 80px 40px at 400px 300px, rgba(255, 255, 255, 0.8) 0%, transparent 70%),
        radial-gradient(ellipse 120px 60px at 700px 150px, rgba(255, 255, 255, 0.9) 0%, transparent 70%),
        radial-gradient(ellipse 90px 45px at 950px 250px, rgba(255, 255, 255, 0.8) 0%, transparent 70%);
    background-size: 100% 100%, 100% 50px, 100% 100%, 100% 100%, 100% 100%, 100% 100%;
    background-position: 0% 0%, 0% 20%, 0% 0%, 0% 0%, 0% 0%, 0% 0%;
    animation: skyFloat 15s linear infinite, rainbowShimmer 5s ease-in-out infinite;
}

@keyframes skyFloat {
    from { background-position: 0% 0%, 0% 20%, 0px 0px, 0px 0px, 0px 0px, 0px 0px; }
    to { background-position: 0% 0%, 100% 20%, -200px 0px, -150px 0px, -180px 0px, -160px 0px; }
}

@keyframes rainbowShimmer {
    0%, 100% { filter: saturate(1); }
    50% { filter: saturate(1.5); }
}

/* Parallax Background Elements */
.background-layer {
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 100%;
    z-index: -1;
}

.background-layer.far {
    animation: parallaxFar 30s linear infinite;
}

.background-layer.mid {
    animation: parallaxMid 20s linear infinite;
}

.background-layer.near {
    animation: parallaxNear 10s linear infinite;
}

@keyframes parallaxFar {
    from { transform: translateX(0); }
    to { transform: translateX(-50%); }
}

@keyframes parallaxMid {
    from { transform: translateX(0); }
    to { transform: translateX(-50%); }
}

@keyframes parallaxNear {
    from { transform: translateX(0); }
    to { transform: translateX(-50%); }
}

/* Weather Effects */
.weather-rain {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        repeating-linear-gradient(
            90deg,
            transparent 0px,
            transparent 2px,
            rgba(173, 216, 230, 0.3) 2px,
            rgba(173, 216, 230, 0.3) 4px
        );
    animation: rain 0.5s linear infinite;
    z-index: 10;
}

@keyframes rain {
    from { transform: translateY(-100%); }
    to { transform: translateY(100%); }
}

.weather-snow {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle 2px at 20px 30px, #FFFFFF 0%, transparent 70%),
        radial-gradient(circle 1px at 80px 80px, #FFFFFF 0%, transparent 70%),
        radial-gradient(circle 3px at 150px 20px, #FFFFFF 0%, transparent 70%),
        radial-gradient(circle 2px at 200px 60px, #FFFFFF 0%, transparent 70%);
    animation: snow 3s linear infinite;
    z-index: 10;
}

@keyframes snow {
    from { transform: translateY(-100%); }
    to { transform: translateY(100%); }
}
