// Coin Classes for Super Mario Game

// Base Coin Class
class Coin {
    constructor(x, y, value = 200) {
        this.x = x;
        this.y = y;
        this.width = 24;
        this.height = 24;
        this.value = value;
        this.collected = false;
        this.animationFrame = 0;
        this.animationTimer = 0;
        this.type = 'coin';
        this.bouncing = false;
        this.bounceHeight = 0;
        this.bounceSpeed = 0;
        this.magnetized = false;
        this.magnetTarget = null;
    }
    
    update(deltaTime, mario = null) {
        this.updateAnimation(deltaTime);
        this.updateBounce(deltaTime);
        this.updateMagnet(deltaTime, mario);
        this.checkCollection(mario);
    }
    
    updateAnimation(deltaTime) {
        this.animationTimer += deltaTime;
        if (this.animationTimer >= 200) {
            this.animationFrame = (this.animationFrame + 1) % 4;
            this.animationTimer = 0;
        }
    }
    
    updateBounce(deltaTime) {
        if (this.bouncing) {
            this.bounceHeight += this.bounceSpeed;
            this.bounceSpeed += 0.5; // Gravity
            
            if (this.bounceHeight >= 0) {
                this.bounceHeight = 0;
                this.bounceSpeed = 0;
                this.bouncing = false;
            }
        }
    }
    
    updateMagnet(deltaTime, mario) {
        if (this.magnetized && mario) {
            const dx = mario.x + mario.width / 2 - (this.x + this.width / 2);
            const dy = mario.y + mario.height / 2 - (this.y + this.height / 2);
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance > 5) {
                const speed = 8;
                this.x += (dx / distance) * speed;
                this.y += (dy / distance) * speed;
            }
        }
    }
    
    checkCollection(mario) {
        if (mario && this.isColliding(mario) && !this.collected) {
            this.collect();
            return true;
        }
        return false;
    }
    
    isColliding(mario) {
        return this.x < mario.x + mario.width &&
               this.x + this.width > mario.x &&
               this.y < mario.y + mario.height &&
               this.y + this.height > mario.y;
    }
    
    collect() {
        this.collected = true;
        this.createCollectionEffect();
        return this.value;
    }
    
    createCollectionEffect() {
        // Create visual collection effect
        this.animationClass = 'collecting';
    }
    
    startBounce(initialSpeed = -8) {
        this.bouncing = true;
        this.bounceSpeed = initialSpeed;
    }
    
    enableMagnet(target) {
        this.magnetized = true;
        this.magnetTarget = target;
    }
    
    getBounds() {
        return {
            x: this.x,
            y: this.y + this.bounceHeight,
            width: this.width,
            height: this.height
        };
    }
    
    getSpriteClass() {
        let classes = ['coin'];
        if (this.collected) classes.push('collecting');
        if (this.bouncing) classes.push('bouncing');
        if (this.magnetized) classes.push('magnetized');
        return classes.join(' ');
    }
}

// Red Coin (Special)
class RedCoin extends Coin {
    constructor(x, y) {
        super(x, y, 800);
        this.type = 'red-coin';
        this.glowTimer = 0;
    }
    
    update(deltaTime, mario = null) {
        super.update(deltaTime, mario);
        this.updateGlow(deltaTime);
    }
    
    updateGlow(deltaTime) {
        this.glowTimer += deltaTime;
    }
    
    getSpriteClass() {
        let classes = ['coin', 'red'];
        if (this.collected) classes.push('collecting');
        if (this.bouncing) classes.push('bouncing');
        if (this.magnetized) classes.push('magnetized');
        return classes.join(' ');
    }
}

// Blue Coin (Extra Special)
class BlueCoin extends Coin {
    constructor(x, y) {
        super(x, y, 1000);
        this.type = 'blue-coin';
        this.pulseTimer = 0;
        this.temporary = true;
        this.lifeTime = 10000; // 10 seconds
        this.timeLeft = this.lifeTime;
    }
    
    update(deltaTime, mario = null) {
        super.update(deltaTime, mario);
        this.updatePulse(deltaTime);
        this.updateLifetime(deltaTime);
    }
    
    updatePulse(deltaTime) {
        this.pulseTimer += deltaTime;
    }
    
    updateLifetime(deltaTime) {
        if (this.temporary) {
            this.timeLeft -= deltaTime;
            if (this.timeLeft <= 0) {
                this.collected = true; // Remove coin
            }
        }
    }
    
    getSpriteClass() {
        let classes = ['coin', 'blue'];
        if (this.collected) classes.push('collecting');
        if (this.bouncing) classes.push('bouncing');
        if (this.magnetized) classes.push('magnetized');
        if (this.timeLeft < 3000) classes.push('warning');
        return classes.join(' ');
    }
}

// Coin Ring
class CoinRing {
    constructor(centerX, centerY, radius = 100, coinCount = 8) {
        this.centerX = centerX;
        this.centerY = centerY;
        this.radius = radius;
        this.coins = [];
        this.rotation = 0;
        this.rotationSpeed = 1;
        this.collected = 0;
        this.totalCoins = coinCount;
        
        this.createCoins(coinCount);
    }
    
    createCoins(count) {
        for (let i = 0; i < count; i++) {
            const angle = (i / count) * Math.PI * 2;
            const x = this.centerX + Math.cos(angle) * this.radius;
            const y = this.centerY + Math.sin(angle) * this.radius;
            
            const coin = new Coin(x, y, 200);
            coin.ringIndex = i;
            coin.ringAngle = angle;
            this.coins.push(coin);
        }
    }
    
    update(deltaTime, mario) {
        this.rotation += this.rotationSpeed * deltaTime * 0.001;
        
        // Update coin positions
        for (let coin of this.coins) {
            if (!coin.collected) {
                const angle = coin.ringAngle + this.rotation;
                coin.x = this.centerX + Math.cos(angle) * this.radius - coin.width / 2;
                coin.y = this.centerY + Math.sin(angle) * this.radius - coin.height / 2;
                
                coin.update(deltaTime, mario);
                
                if (coin.collected) {
                    this.collected++;
                }
            }
        }
        
        // Check if all coins collected
        return this.collected >= this.totalCoins;
    }
    
    getScore() {
        return this.collected * 200 + (this.collected === this.totalCoins ? 2000 : 0);
    }
    
    getBounds() {
        return {
            x: this.centerX - this.radius,
            y: this.centerY - this.radius,
            width: this.radius * 2,
            height: this.radius * 2
        };
    }
}

// Coin Trail
class CoinTrail {
    constructor(startX, startY, endX, endY, coinCount = 5) {
        this.coins = [];
        this.createTrail(startX, startY, endX, endY, coinCount);
    }
    
    createTrail(startX, startY, endX, endY, count) {
        for (let i = 0; i < count; i++) {
            const progress = i / (count - 1);
            const x = startX + (endX - startX) * progress;
            const y = startY + (endY - startY) * progress;
            
            const coin = new Coin(x, y, 200);
            coin.trailIndex = i;
            this.coins.push(coin);
        }
    }
    
    update(deltaTime, mario) {
        let allCollected = true;
        
        for (let coin of this.coins) {
            if (!coin.collected) {
                coin.update(deltaTime, mario);
                allCollected = false;
            }
        }
        
        return allCollected;
    }
    
    getScore() {
        let score = 0;
        for (let coin of this.coins) {
            if (coin.collected) {
                score += coin.value;
            }
        }
        return score;
    }
}

// Coin Fountain
class CoinFountain {
    constructor(x, y, coinCount = 10) {
        this.x = x;
        this.y = y;
        this.coins = [];
        this.active = false;
        this.duration = 3000; // 3 seconds
        this.timeLeft = 0;
        this.spawnTimer = 0;
        this.spawnInterval = 200; // 200ms between coins
        this.coinsToSpawn = coinCount;
        this.coinsSpawned = 0;
    }
    
    activate() {
        this.active = true;
        this.timeLeft = this.duration;
        this.coinsSpawned = 0;
    }
    
    update(deltaTime, mario) {
        if (!this.active) return false;
        
        this.timeLeft -= deltaTime;
        this.spawnTimer += deltaTime;
        
        // Spawn new coins
        if (this.spawnTimer >= this.spawnInterval && this.coinsSpawned < this.coinsToSpawn) {
            this.spawnCoin();
            this.spawnTimer = 0;
            this.coinsSpawned++;
        }
        
        // Update existing coins
        for (let i = this.coins.length - 1; i >= 0; i--) {
            const coin = this.coins[i];
            coin.update(deltaTime, mario);
            
            // Remove collected or fallen coins
            if (coin.collected || coin.y > 650) {
                this.coins.splice(i, 1);
            }
        }
        
        // Check if fountain is done
        if (this.timeLeft <= 0 && this.coins.length === 0) {
            this.active = false;
            return true; // Fountain complete
        }
        
        return false;
    }
    
    spawnCoin() {
        const coin = new Coin(this.x, this.y, 200);
        
        // Random fountain trajectory
        const angle = Math.random() * Math.PI - Math.PI / 2; // -90 to 90 degrees
        const speed = 5 + Math.random() * 5; // 5-10 speed
        
        coin.velocityX = Math.cos(angle) * speed;
        coin.velocityY = Math.sin(angle) * speed - 8; // Initial upward velocity
        coin.fountain = true;
        
        this.coins.push(coin);
    }
    
    getScore() {
        let score = 0;
        for (let coin of this.coins) {
            if (coin.collected) {
                score += coin.value;
            }
        }
        return score;
    }
}

// Coin Block (spawns coins when hit)
class CoinBlock {
    constructor(x, y, coinCount = 10) {
        this.x = x;
        this.y = y;
        this.width = 32;
        this.height = 32;
        this.coinCount = coinCount;
        this.coinsLeft = coinCount;
        this.hitTimer = 0;
        this.active = false;
    }
    
    onHit() {
        if (this.coinsLeft > 0) {
            this.coinsLeft--;
            this.hitTimer = 500; // 500ms hit animation
            this.active = true;
            
            // Spawn coin
            const coin = new Coin(this.x, this.y - 32, 200);
            coin.startBounce(-10);
            
            return coin;
        }
        return null;
    }
    
    update(deltaTime) {
        if (this.hitTimer > 0) {
            this.hitTimer -= deltaTime;
            if (this.hitTimer <= 0) {
                this.active = false;
            }
        }
    }
    
    isEmpty() {
        return this.coinsLeft <= 0;
    }
    
    getBounds() {
        return {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height
        };
    }
    
    getSpriteClass() {
        let classes = ['question-block'];
        if (this.isEmpty()) classes.push('used');
        if (this.active) classes.push('hit');
        return classes.join(' ');
    }
}

// Export classes
window.Coin = Coin;
window.RedCoin = RedCoin;
window.BlueCoin = BlueCoin;
window.CoinRing = CoinRing;
window.CoinTrail = CoinTrail;
window.CoinFountain = CoinFountain;
window.CoinBlock = CoinBlock;
