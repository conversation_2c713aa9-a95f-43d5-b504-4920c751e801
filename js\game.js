// Main Game Engine for Super Mario

class Game {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.width = this.canvas.width;
        this.height = this.canvas.height;
        
        // Game state
        this.gameState = 'start'; // start, playing, paused, gameOver, levelComplete, victory
        this.score = 0;
        this.lives = 3;
        this.time = 400;
        this.currentLevel = 1;
        
        // Game objects
        this.mario = null;
        this.physics = new PhysicsEngine();
        this.levelManager = new LevelManager();
        this.audioManager = new AudioManager();
        this.powerUpSpawner = new PowerUpSpawner();
        
        // Camera
        this.camera = { x: 0, y: 0 };
        
        // Input handling
        this.keys = {};
        this.setupInput();
        
        // Game loop
        this.lastTime = 0;
        this.gameLoop = this.gameLoop.bind(this);
        
        // UI elements
        this.setupUI();
        
        // Initialize game
        this.init();
    }
    
    init() {
        this.showStartScreen();
        this.audioManager.preloadAudio();
    }
    
    setupInput() {
        document.addEventListener('keydown', (e) => {
            this.keys[e.code] = true;
            this.handleKeyDown(e);
        });
        
        document.addEventListener('keyup', (e) => {
            this.keys[e.code] = false;
        });
        
        // Prevent default behavior for game keys
        document.addEventListener('keydown', (e) => {
            if (['Space', 'ArrowLeft', 'ArrowRight', 'ArrowUp', 'ArrowDown'].includes(e.code)) {
                e.preventDefault();
            }
        });
    }
    
    handleKeyDown(e) {
        switch (e.code) {
            case 'Space':
                if (this.gameState === 'start') {
                    this.startGame();
                } else if (this.gameState === 'playing') {
                    this.mario.keys.jump = true;
                } else if (this.gameState === 'gameOver' || this.gameState === 'levelComplete') {
                    this.restartGame();
                }
                break;
            case 'Escape':
                if (this.gameState === 'playing') {
                    this.pauseGame();
                } else if (this.gameState === 'paused') {
                    this.resumeGame();
                }
                break;
        }
    }
    
    setupUI() {
        // Update UI elements
        this.updateUI();
        
        // Setup screen event listeners
        this.setupScreenEvents();
    }
    
    setupScreenEvents() {
        // Start screen
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space' && this.gameState === 'start') {
                this.startGame();
            }
        });
    }
    
    startGame() {
        this.gameState = 'playing';
        this.hideAllScreens();
        
        // Initialize Mario
        const startPos = this.levelManager.getCurrentLevel().getStartPosition();
        this.mario = new Mario(startPos.x, startPos.y);
        this.mario.lives = this.lives;
        
        // Start level music
        this.audioManager.startLevelMusic(this.currentLevel);
        
        // Start game loop
        requestAnimationFrame(this.gameLoop);
        
        // Start timer
        this.startTimer();
    }
    
    startTimer() {
        this.timeInterval = setInterval(() => {
            if (this.gameState === 'playing') {
                this.time--;
                if (this.time <= 0) {
                    this.mario.die();
                }
                this.updateUI();
            }
        }, 1000);
    }
    
    gameLoop(currentTime) {
        if (this.gameState !== 'playing') return;
        
        const deltaTime = currentTime - this.lastTime;
        this.lastTime = currentTime;
        
        this.update(deltaTime);
        this.render();
        
        requestAnimationFrame(this.gameLoop);
    }
    
    update(deltaTime) {
        const level = this.levelManager.getCurrentLevel();
        
        // Update Mario input
        this.updateMarioInput();
        
        // Update Mario
        this.mario.update(deltaTime, this.physics, level.getBlocks(), [], []);
        
        // Update enemies
        level.getEnemies().forEach(enemy => {
            enemy.update(deltaTime, this.physics, level.getBlocks(), []);
        });
        
        // Update coins
        level.getCoins().forEach(coin => {
            if (coin.update) {
                coin.update(deltaTime, this.mario);
            }
        });
        
        // Update power-ups
        const powerUpResult = this.powerUpSpawner.update(deltaTime, this.physics, level.getBlocks(), [], this.mario);
        if (powerUpResult) {
            this.score += powerUpResult.score;
            this.audioManager.onPowerUpCollect();
        }
        
        // Check collisions
        this.checkCollisions();
        
        // Update camera
        this.updateCamera();
        
        // Update UI
        this.updateUI();
    }
    
    updateMarioInput() {
        this.mario.keys.left = this.keys['ArrowLeft'];
        this.mario.keys.right = this.keys['ArrowRight'];
        this.mario.keys.jump = this.keys['Space'];
        this.mario.keys.run = this.keys['ShiftLeft'] || this.keys['ShiftRight'];
        this.mario.keys.fire = this.keys['KeyX'];
        this.mario.keys.down = this.keys['ArrowDown'];
    }
    
    checkCollisions() {
        const level = this.levelManager.getCurrentLevel();
        
        // Mario vs Enemies
        level.getEnemies().forEach((enemy, index) => {
            if (enemy.alive && this.physics.checkCollision(this.mario.getBounds(), enemy.getBounds())) {
                const side = this.physics.getCollisionSide(this.mario.getBounds(), enemy.getBounds());
                
                if (side === 'top' && this.mario.velocityY > 0) {
                    // Mario stomped enemy
                    const points = enemy.takeDamage('stomp');
                    this.score += points;
                    this.mario.velocityY = -8; // Bounce
                    this.audioManager.onEnemyStomp();
                } else if (!this.mario.invincible) {
                    // Mario hit enemy
                    const died = this.mario.takeDamage();
                    if (died) {
                        this.gameOver();
                    }
                }
            }
        });
        
        // Mario vs Coins
        level.getCoins().forEach((coin, index) => {
            if (coin.checkCollection && coin.checkCollection(this.mario)) {
                this.score += coin.value;
                this.audioManager.onCoinCollect();
                
                // Check for 1-up (100 coins)
                if (Math.floor(this.score / 20000) > Math.floor((this.score - coin.value) / 20000)) {
                    this.mario.lives++;
                    this.audioManager.onOneUpGain();
                }
            }
        });
        
        // Mario vs Blocks
        level.getBlocks().forEach(block => {
            if (this.physics.checkCollision(this.mario.getBounds(), block.getBounds())) {
                const side = this.physics.getCollisionSide(this.mario.getBounds(), block.getBounds());
                const result = block.onHit(this.mario, side);
                
                if (result) {
                    if (result.type === 'levelComplete') {
                        this.levelComplete();
                    } else if (result.type === 'warp') {
                        this.warpToLevel(result.destination);
                    } else if (result.type === 'flagTouch') {
                        this.score += result.score;
                        this.audioManager.onFlagpoleTouch();
                        setTimeout(() => this.levelComplete(), 2000);
                    }
                }
            }
        });
        
        // Mario vs Fireballs vs Enemies
        this.mario.fireballs.forEach((fireball, fireballIndex) => {
            level.getEnemies().forEach((enemy, enemyIndex) => {
                if (enemy.alive && this.physics.checkCollision(fireball.getBounds(), enemy.getBounds())) {
                    const points = enemy.takeDamage('fireball');
                    this.score += points;
                    this.mario.fireballs.splice(fireballIndex, 1);
                }
            });
        });
    }
    
    updateCamera() {
        // Follow Mario
        this.camera.x = this.mario.x - this.width / 3;
        this.camera.y = 0;
        
        // Clamp camera to level bounds
        const level = this.levelManager.getCurrentLevel();
        this.camera.x = Math.max(0, Math.min(level.width - this.width, this.camera.x));
    }
    
    render() {
        // Clear canvas
        this.ctx.clearRect(0, 0, this.width, this.height);
        
        // Set camera transform
        this.ctx.save();
        this.ctx.translate(-this.camera.x, -this.camera.y);
        
        // Render level background
        this.renderBackground();
        
        // Render level objects
        this.renderLevel();
        
        // Render Mario
        this.renderMario();
        
        // Render enemies
        this.renderEnemies();
        
        // Render coins
        this.renderCoins();
        
        // Render power-ups
        this.renderPowerUps();
        
        // Render effects
        this.renderEffects();
        
        this.ctx.restore();
    }
    
    renderBackground() {
        const level = this.levelManager.getCurrentLevel();
        document.body.className = 'background ' + level.getBackgroundClass();
    }
    
    renderLevel() {
        const level = this.levelManager.getCurrentLevel();
        
        // Render blocks
        level.getBlocks().forEach(block => {
            if (this.isInView(block.getBounds())) {
                this.renderBlock(block);
            }
        });
    }
    
    renderMario() {
        if (this.mario) {
            this.renderSprite(this.mario, this.mario.getSpriteClass());
        }
    }
    
    renderEnemies() {
        const level = this.levelManager.getCurrentLevel();
        level.getEnemies().forEach(enemy => {
            if (enemy.alive && this.isInView(enemy.getBounds())) {
                this.renderSprite(enemy, enemy.getSpriteClass());
            }
        });
    }
    
    renderCoins() {
        const level = this.levelManager.getCurrentLevel();
        level.getCoins().forEach(coin => {
            if (!coin.collected && this.isInView(coin.getBounds())) {
                this.renderSprite(coin, coin.getSpriteClass());
            }
        });
    }
    
    renderPowerUps() {
        this.powerUpSpawner.getPowerUps().forEach(powerUp => {
            if (!powerUp.collected && this.isInView(powerUp.getBounds())) {
                this.renderSprite(powerUp, powerUp.getSpriteClass());
            }
        });
    }
    
    renderEffects() {
        // Render Mario's fireballs
        if (this.mario) {
            this.mario.fireballs.forEach(fireball => {
                this.renderSprite(fireball, 'fireball');
            });
        }
    }
    
    renderSprite(obj, className) {
        // Create a temporary div element for CSS rendering
        const element = document.createElement('div');
        element.className = className;
        element.style.position = 'absolute';
        element.style.left = obj.x + 'px';
        element.style.top = obj.y + 'px';
        element.style.width = obj.width + 'px';
        element.style.height = obj.height + 'px';
        
        // In a real implementation, this would be rendered to canvas
        // For now, we'll use a simple rectangle
        this.ctx.fillStyle = this.getColorForClass(className);
        this.ctx.fillRect(obj.x, obj.y, obj.width, obj.height);
    }
    
    renderBlock(block) {
        this.ctx.fillStyle = this.getColorForClass(block.getSpriteClass());
        this.ctx.fillRect(block.x, block.y, block.width, block.height);
        
        // Add border for visibility
        this.ctx.strokeStyle = '#000';
        this.ctx.lineWidth = 1;
        this.ctx.strokeRect(block.x, block.y, block.width, block.height);
    }
    
    getColorForClass(className) {
        // Simple color mapping for different sprites
        if (className.includes('mario')) return '#FF0000';
        if (className.includes('goomba')) return '#8B4513';
        if (className.includes('koopa')) return '#00AA00';
        if (className.includes('coin')) return '#FFD700';
        if (className.includes('ground')) return '#8B4513';
        if (className.includes('brick')) return '#CD853F';
        if (className.includes('question')) return '#FFD700';
        if (className.includes('pipe')) return '#228B22';
        if (className.includes('mushroom')) return '#FF0000';
        if (className.includes('flower')) return '#FF4500';
        if (className.includes('star')) return '#FFD700';
        return '#808080'; // Default gray
    }
    
    isInView(bounds) {
        return bounds.x + bounds.width > this.camera.x &&
               bounds.x < this.camera.x + this.width &&
               bounds.y + bounds.height > this.camera.y &&
               bounds.y < this.camera.y + this.height;
    }
    
    updateUI() {
        document.getElementById('scoreValue').textContent = this.score;
        document.getElementById('livesValue').textContent = this.mario ? this.mario.lives : this.lives;
        document.getElementById('levelValue').textContent = this.currentLevel;
        document.getElementById('timeValue').textContent = this.time;
    }
    
    levelComplete() {
        this.gameState = 'levelComplete';
        this.audioManager.onLevelComplete();
        
        if (this.levelManager.isLastLevel()) {
            this.victory();
        } else {
            this.showLevelCompleteScreen();
        }
    }
    
    nextLevel() {
        this.currentLevel++;
        const nextLevel = this.levelManager.nextLevel();
        
        if (nextLevel) {
            this.time = nextLevel.timeLimit;
            this.startGame();
        } else {
            this.victory();
        }
    }
    
    victory() {
        this.gameState = 'victory';
        this.audioManager.onLevelComplete();
        this.showVictoryScreen();
    }
    
    gameOver() {
        this.gameState = 'gameOver';
        this.audioManager.onGameOver();
        this.showGameOverScreen();
        
        if (this.timeInterval) {
            clearInterval(this.timeInterval);
        }
    }
    
    restartGame() {
        this.score = 0;
        this.lives = 3;
        this.time = 400;
        this.currentLevel = 1;
        this.levelManager.resetToLevel(1);
        this.powerUpSpawner.clear();
        this.startGame();
    }
    
    pauseGame() {
        this.gameState = 'paused';
        this.audioManager.pauseMusic();
        this.showPauseScreen();
    }
    
    resumeGame() {
        this.gameState = 'playing';
        this.audioManager.resumeMusic();
        this.hideAllScreens();
        requestAnimationFrame(this.gameLoop);
    }
    
    // Screen management
    showStartScreen() {
        this.hideAllScreens();
        document.getElementById('startScreen').classList.remove('hidden');
    }
    
    showGameOverScreen() {
        this.hideAllScreens();
        document.getElementById('gameOverScreen').classList.remove('hidden');
        document.getElementById('finalScore').textContent = this.score;
    }
    
    showLevelCompleteScreen() {
        this.hideAllScreens();
        document.getElementById('levelCompleteScreen').classList.remove('hidden');
        document.getElementById('completedLevel').textContent = this.currentLevel;
    }
    
    showVictoryScreen() {
        this.hideAllScreens();
        document.getElementById('victoryScreen').classList.remove('hidden');
        document.getElementById('victoryScore').textContent = this.score;
    }
    
    showPauseScreen() {
        // Create pause overlay if it doesn't exist
        let pauseScreen = document.getElementById('pauseScreen');
        if (!pauseScreen) {
            pauseScreen = document.createElement('div');
            pauseScreen.id = 'pauseScreen';
            pauseScreen.className = 'screen';
            pauseScreen.innerHTML = `
                <h1>مؤقت</h1>
                <p>اضغط Escape للمتابعة</p>
            `;
            document.getElementById('gameContainer').appendChild(pauseScreen);
        }
        pauseScreen.classList.remove('hidden');
    }
    
    hideAllScreens() {
        document.querySelectorAll('.screen').forEach(screen => {
            screen.classList.add('hidden');
        });
    }
}

// Global functions for button events
window.restartGame = function() {
    if (window.game) {
        window.game.restartGame();
    }
};

window.nextLevel = function() {
    if (window.game) {
        window.game.nextLevel();
    }
};

// Initialize game when page loads
document.addEventListener('DOMContentLoaded', () => {
    window.game = new Game();
});
