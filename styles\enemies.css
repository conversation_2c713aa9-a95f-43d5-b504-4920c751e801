/* Enemy Styles */

/* <PERSON><PERSON><PERSON> Enemy */
.goomba {
    position: absolute;
    width: 32px;
    height: 32px;
    z-index: 40;
}

.goomba .goomba-sprite {
    width: 32px;
    height: 32px;
    background: 
        /* Body */
        radial-gradient(ellipse 16px 20px at 16px 20px, #8B4513 0%, #654321 70%, transparent 70%),
        /* Eyes */
        radial-gradient(circle 3px at 10px 12px, #000 0%, #000 100%, transparent 100%),
        radial-gradient(circle 3px at 22px 12px, #000 0%, #000 100%, transparent 100%),
        /* Eyebrows */
        linear-gradient(45deg, #654321 0%, #654321 100%);
    position: relative;
    border-radius: 50% 50% 20% 20%;
    animation: goombaWalk 1s ease-in-out infinite;
}

@keyframes goombaWalk {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(2px); }
}

.goomba.squashed {
    animation: squash 0.3s ease-out forwards;
}

@keyframes squash {
    0% { transform: scaleY(1); }
    100% { transform: scaleY(0.2); opacity: 0.5; }
}

/* <PERSON><PERSON><PERSON>a */
.koopa {
    position: absolute;
    width: 32px;
    height: 48px;
    z-index: 40;
}

.koopa .koopa-sprite {
    width: 32px;
    height: 48px;
    background: 
        /* Shell */
        radial-gradient(ellipse 16px 12px at 16px 35px, #00AA00 0%, #008800 70%, transparent 70%),
        /* Body */
        radial-gradient(ellipse 12px 16px at 16px 20px, #FFFF99 0%, #FFFF77 70%, transparent 70%),
        /* Head */
        radial-gradient(circle 8px at 16px 10px, #FFFF99 0%, #FFFF77 70%, transparent 70%);
    position: relative;
    animation: koopaWalk 0.8s ease-in-out infinite;
}

@keyframes koopaWalk {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(1px) rotate(2deg); }
    75% { transform: translateX(-1px) rotate(-2deg); }
}

.koopa.shell-only {
    height: 24px;
    animation: shellSpin 0.5s linear infinite;
}

.koopa.shell-only .koopa-sprite {
    height: 24px;
    background: radial-gradient(ellipse 16px 12px at 16px 12px, #00AA00 0%, #008800 70%, transparent 70%);
}

@keyframes shellSpin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Piranha Plant */
.piranha {
    position: absolute;
    width: 32px;
    height: 48px;
    z-index: 40;
}

.piranha .piranha-sprite {
    width: 32px;
    height: 48px;
    background: 
        /* Stem */
        linear-gradient(to bottom, #228B22 0%, #006400 100%),
        /* Head */
        radial-gradient(ellipse 16px 20px at 16px 15px, #FF0000 0%, #CC0000 70%, transparent 70%),
        /* Spots */
        radial-gradient(circle 3px at 10px 10px, #FFFFFF 0%, #FFFFFF 100%, transparent 100%),
        radial-gradient(circle 3px at 22px 12px, #FFFFFF 0%, #FFFFFF 100%, transparent 100%),
        radial-gradient(circle 2px at 16px 8px, #FFFFFF 0%, #FFFFFF 100%, transparent 100%);
    position: relative;
    animation: piranhaChomp 1.5s ease-in-out infinite;
}

@keyframes piranhaChomp {
    0%, 100% { transform: translateY(0) scaleY(1); }
    50% { transform: translateY(-10px) scaleY(1.1); }
}

.piranha.emerging {
    animation: emerge 2s ease-in-out;
}

@keyframes emerge {
    0% { transform: translateY(48px); }
    50% { transform: translateY(0); }
    100% { transform: translateY(48px); }
}

/* Bullet Bill */
.bullet {
    position: absolute;
    width: 32px;
    height: 16px;
    z-index: 40;
}

.bullet .bullet-sprite {
    width: 32px;
    height: 16px;
    background: 
        /* Body */
        linear-gradient(to right, #333333 0%, #666666 50%, #333333 100%),
        /* Tip */
        radial-gradient(circle 8px at 28px 8px, #FF0000 0%, #CC0000 70%, transparent 70%);
    position: relative;
    border-radius: 8px;
    animation: bulletFly 0.1s linear infinite;
}

@keyframes bulletFly {
    0%, 100% { transform: translateX(0); }
    50% { transform: translateX(1px); }
}

/* Lakitu */
.lakitu {
    position: absolute;
    width: 48px;
    height: 48px;
    z-index: 45;
}

.lakitu .lakitu-sprite {
    width: 48px;
    height: 48px;
    background: 
        /* Cloud */
        radial-gradient(ellipse 24px 16px at 24px 32px, #FFFFFF 0%, #E0E0E0 70%, transparent 70%),
        /* Body */
        radial-gradient(ellipse 16px 20px at 24px 20px, #00AA00 0%, #008800 70%, transparent 70%),
        /* Glasses */
        radial-gradient(circle 6px at 20px 15px, #000000 0%, #000000 100%, transparent 100%),
        radial-gradient(circle 6px at 28px 15px, #000000 0%, #000000 100%, transparent 100%);
    position: relative;
    animation: lakituFloat 3s ease-in-out infinite;
}

@keyframes lakituFloat {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

/* Spiny */
.spiny {
    position: absolute;
    width: 32px;
    height: 24px;
    z-index: 40;
}

.spiny .spiny-sprite {
    width: 32px;
    height: 24px;
    background: 
        /* Shell */
        radial-gradient(ellipse 16px 12px at 16px 12px, #FF0000 0%, #CC0000 70%, transparent 70%),
        /* Spikes */
        conic-gradient(from 0deg at 8px 6px, #FFFF00 0deg, transparent 45deg),
        conic-gradient(from 0deg at 24px 6px, #FFFF00 0deg, transparent 45deg),
        conic-gradient(from 0deg at 16px 3px, #FFFF00 0deg, transparent 45deg);
    position: relative;
    border-radius: 50%;
    animation: spinyRoll 0.6s linear infinite;
}

@keyframes spinyRoll {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Enemy Death Effects */
.enemy.dying {
    animation: enemyDeath 0.5s ease-out forwards;
}

@keyframes enemyDeath {
    0% { transform: translateY(0) rotate(0deg); opacity: 1; }
    100% { transform: translateY(-50px) rotate(180deg); opacity: 0; }
}

/* Enemy Hit Effect */
.enemy.hit {
    animation: enemyHit 0.3s ease-out;
}

@keyframes enemyHit {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-5px); }
    75% { transform: translateX(5px); }
}

/* Enemy Spawn Effect */
.enemy.spawning {
    animation: enemySpawn 0.5s ease-out;
}

@keyframes enemySpawn {
    0% { transform: scale(0); opacity: 0; }
    50% { transform: scale(1.2); opacity: 0.8; }
    100% { transform: scale(1); opacity: 1; }
}
