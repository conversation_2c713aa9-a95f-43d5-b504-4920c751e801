// Audio Manager for Super Mario Game

class AudioManager {
    constructor() {
        this.sounds = {};
        this.music = {};
        this.currentMusic = null;
        this.musicVolume = 0.3;
        this.soundVolume = 0.5;
        this.muted = false;
        this.musicMuted = false;
        
        this.initializeAudio();
    }
    
    initializeAudio() {
        // Initialize sound effects
        this.sounds = {
            jump: this.createAudioElement('sounds/jump.wav'),
            coin: this.createAudioElement('sounds/coin.wav'),
            powerup: this.createAudioElement('sounds/powerup.wav'),
            fireball: this.createAudioElement('sounds/fireball.wav'),
            stomp: this.createAudioElement('sounds/stomp.wav'),
            break: this.createAudioElement('sounds/break.wav'),
            death: this.createAudioElement('sounds/death.wav'),
            gameOver: this.createAudioElement('sounds/gameover.wav'),
            victory: this.createAudioElement('sounds/victory.wav'),
            flagpole: this.createAudioElement('sounds/flagpole.wav'),
            oneUp: this.createAudioElement('sounds/1up.wav'),
            star: this.createAudioElement('sounds/star.wav'),
            pipe: this.createAudioElement('sounds/pipe.wav'),
            kick: this.createAudioElement('sounds/kick.wav'),
            bump: this.createAudioElement('sounds/bump.wav')
        };
        
        // Initialize music tracks
        this.music = {
            overworld: this.createAudioElement('sounds/overworld.mp3', true),
            underground: this.createAudioElement('sounds/underground.mp3', true),
            underwater: this.createAudioElement('sounds/underwater.mp3', true),
            castle: this.createAudioElement('sounds/castle.mp3', true),
            sky: this.createAudioElement('sounds/sky.mp3', true),
            boss: this.createAudioElement('sounds/boss.mp3', true),
            invincible: this.createAudioElement('sounds/invincible.mp3', true)
        };
        
        // Set initial volumes
        this.setMusicVolume(this.musicVolume);
        this.setSoundVolume(this.soundVolume);
    }
    
    createAudioElement(src, isMusic = false) {
        // Create a simple audio object that can be played
        return {
            src: src,
            volume: isMusic ? this.musicVolume : this.soundVolume,
            loop: isMusic,
            currentTime: 0,
            paused: true,
            play: function() {
                // In a real implementation, this would play the audio
                console.log(`Playing: ${this.src}`);
                this.paused = false;
                return Promise.resolve();
            },
            pause: function() {
                this.paused = true;
            },
            stop: function() {
                this.pause();
                this.currentTime = 0;
            }
        };
    }
    
    playSound(soundName) {
        if (this.muted || !this.sounds[soundName]) return;
        
        try {
            const sound = this.sounds[soundName];
            sound.currentTime = 0;
            sound.volume = this.soundVolume;
            sound.play();
        } catch (error) {
            console.warn(`Could not play sound: ${soundName}`);
        }
    }
    
    playMusic(musicName) {
        if (this.musicMuted || !this.music[musicName]) return;
        
        // Stop current music
        this.stopMusic();
        
        try {
            this.currentMusic = this.music[musicName];
            this.currentMusic.volume = this.musicVolume;
            this.currentMusic.loop = true;
            this.currentMusic.play();
        } catch (error) {
            console.warn(`Could not play music: ${musicName}`);
        }
    }
    
    stopMusic() {
        if (this.currentMusic) {
            this.currentMusic.stop();
            this.currentMusic = null;
        }
    }
    
    pauseMusic() {
        if (this.currentMusic && !this.currentMusic.paused) {
            this.currentMusic.pause();
        }
    }
    
    resumeMusic() {
        if (this.currentMusic && this.currentMusic.paused && !this.musicMuted) {
            this.currentMusic.play();
        }
    }
    
    setMusicVolume(volume) {
        this.musicVolume = Math.max(0, Math.min(1, volume));
        
        Object.values(this.music).forEach(track => {
            track.volume = this.musicVolume;
        });
        
        if (this.currentMusic) {
            this.currentMusic.volume = this.musicVolume;
        }
    }
    
    setSoundVolume(volume) {
        this.soundVolume = Math.max(0, Math.min(1, volume));
        
        Object.values(this.sounds).forEach(sound => {
            sound.volume = this.soundVolume;
        });
    }
    
    toggleMute() {
        this.muted = !this.muted;
        if (this.muted) {
            this.setSoundVolume(0);
        } else {
            this.setSoundVolume(0.5);
        }
    }
    
    toggleMusicMute() {
        this.musicMuted = !this.musicMuted;
        if (this.musicMuted) {
            this.pauseMusic();
        } else {
            this.resumeMusic();
        }
    }
    
    // Game event sound mappings
    onMarioJump() {
        this.playSound('jump');
    }
    
    onCoinCollect() {
        this.playSound('coin');
    }
    
    onPowerUpCollect() {
        this.playSound('powerup');
    }
    
    onEnemyStomp() {
        this.playSound('stomp');
    }
    
    onBlockBreak() {
        this.playSound('break');
    }
    
    onMarioDeath() {
        this.stopMusic();
        this.playSound('death');
    }
    
    onGameOver() {
        this.stopMusic();
        this.playSound('gameOver');
    }
    
    onLevelComplete() {
        this.stopMusic();
        this.playSound('victory');
    }
    
    onFlagpoleTouch() {
        this.stopMusic();
        this.playSound('flagpole');
    }
    
    onOneUpGain() {
        this.playSound('oneUp');
    }
    
    onStarPowerActivate() {
        this.stopMusic();
        this.playMusic('invincible');
    }
    
    onStarPowerEnd() {
        this.stopMusic();
        // Resume level music based on current level
        this.resumeLevelMusic();
    }
    
    onFireballShoot() {
        this.playSound('fireball');
    }
    
    onPipeEnter() {
        this.playSound('pipe');
    }
    
    onShellKick() {
        this.playSound('kick');
    }
    
    onBlockBump() {
        this.playSound('bump');
    }
    
    // Level-specific music
    startLevelMusic(levelNumber) {
        switch (levelNumber) {
            case 1:
                this.playMusic('overworld');
                break;
            case 2:
                this.playMusic('underground');
                break;
            case 3:
                this.playMusic('underwater');
                break;
            case 4:
                this.playMusic('castle');
                break;
            case 5:
                this.playMusic('sky');
                break;
            default:
                this.playMusic('overworld');
        }
    }
    
    resumeLevelMusic() {
        // This would need to know the current level
        // For now, default to overworld
        this.playMusic('overworld');
    }
    
    // Audio context management for better performance
    createAudioContext() {
        try {
            window.AudioContext = window.AudioContext || window.webkitAudioContext;
            this.audioContext = new AudioContext();
            return true;
        } catch (error) {
            console.warn('Web Audio API not supported');
            return false;
        }
    }
    
    // Preload audio files
    preloadAudio() {
        const audioPromises = [];
        
        // Preload sounds
        Object.values(this.sounds).forEach(sound => {
            if (sound.src) {
                audioPromises.push(this.loadAudio(sound.src));
            }
        });
        
        // Preload music
        Object.values(this.music).forEach(track => {
            if (track.src) {
                audioPromises.push(this.loadAudio(track.src));
            }
        });
        
        return Promise.all(audioPromises);
    }
    
    loadAudio(src) {
        return new Promise((resolve, reject) => {
            // In a real implementation, this would load the actual audio file
            console.log(`Loading audio: ${src}`);
            setTimeout(() => resolve(src), 100); // Simulate loading
        });
    }
    
    // Audio effects
    playRandomPitchSound(soundName, pitchVariation = 0.2) {
        if (this.muted || !this.sounds[soundName]) return;
        
        const sound = this.sounds[soundName];
        const pitch = 1 + (Math.random() - 0.5) * pitchVariation;
        
        // In a real implementation, this would modify the playback rate
        console.log(`Playing ${soundName} with pitch: ${pitch}`);
        this.playSound(soundName);
    }
    
    fadeOutMusic(duration = 1000) {
        if (!this.currentMusic) return;
        
        const startVolume = this.currentMusic.volume;
        const fadeStep = startVolume / (duration / 50);
        
        const fadeInterval = setInterval(() => {
            if (this.currentMusic.volume > fadeStep) {
                this.currentMusic.volume -= fadeStep;
            } else {
                this.currentMusic.volume = 0;
                this.stopMusic();
                clearInterval(fadeInterval);
            }
        }, 50);
    }
    
    fadeInMusic(musicName, duration = 1000) {
        this.playMusic(musicName);
        if (!this.currentMusic) return;
        
        this.currentMusic.volume = 0;
        const targetVolume = this.musicVolume;
        const fadeStep = targetVolume / (duration / 50);
        
        const fadeInterval = setInterval(() => {
            if (this.currentMusic.volume < targetVolume - fadeStep) {
                this.currentMusic.volume += fadeStep;
            } else {
                this.currentMusic.volume = targetVolume;
                clearInterval(fadeInterval);
            }
        }, 50);
    }
    
    // Cleanup
    destroy() {
        this.stopMusic();
        Object.values(this.sounds).forEach(sound => {
            if (sound.stop) sound.stop();
        });
        Object.values(this.music).forEach(track => {
            if (track.stop) track.stop();
        });
        
        if (this.audioContext) {
            this.audioContext.close();
        }
    }
}

// Export for use in other files
window.AudioManager = AudioManager;
