// Audio Manager for Super Mario Game

class AudioManager {
    constructor() {
        this.sounds = {};
        this.music = {};
        this.currentMusic = null;
        this.musicVolume = 0.3;
        this.soundVolume = 0.5;
        this.muted = false;
        this.musicMuted = false;
        this.audioContext = null;

        this.createAudioContext();
        this.initializeAudio();
    }

    initializeAudio() {
        // Initialize sound effects
        this.sounds = {
            jump: this.createAudioElement('sounds/jump.wav'),
            coin: this.createAudioElement('sounds/coin.wav'),
            powerup: this.createAudioElement('sounds/powerup.wav'),
            fireball: this.createAudioElement('sounds/fireball.wav'),
            stomp: this.createAudioElement('sounds/stomp.wav'),
            break: this.createAudioElement('sounds/break.wav'),
            death: this.createAudioElement('sounds/death.wav'),
            gameOver: this.createAudioElement('sounds/gameover.wav'),
            victory: this.createAudioElement('sounds/victory.wav'),
            flagpole: this.createAudioElement('sounds/flagpole.wav'),
            oneUp: this.createAudioElement('sounds/1up.wav'),
            star: this.createAudioElement('sounds/star.wav'),
            pipe: this.createAudioElement('sounds/pipe.wav'),
            kick: this.createAudioElement('sounds/kick.wav'),
            bump: this.createAudioElement('sounds/bump.wav')
        };

        // Initialize music tracks
        this.music = {
            overworld: this.createAudioElement('sounds/overworld.mp3', true),
            underground: this.createAudioElement('sounds/underground.mp3', true),
            underwater: this.createAudioElement('sounds/underwater.mp3', true),
            castle: this.createAudioElement('sounds/castle.mp3', true),
            sky: this.createAudioElement('sounds/sky.mp3', true),
            boss: this.createAudioElement('sounds/boss.mp3', true),
            invincible: this.createAudioElement('sounds/invincible.mp3', true)
        };

        // Set initial volumes
        this.setMusicVolume(this.musicVolume);
        this.setSoundVolume(this.soundVolume);
    }

    createAudioElement(src, isMusic = false) {
        // Create synthetic audio using Web Audio API
        return {
            src: src,
            volume: isMusic ? this.musicVolume : this.soundVolume,
            loop: isMusic,
            currentTime: 0,
            paused: true,
            play: function() {
                // Generate synthetic sound based on filename
                if (window.audioManager && window.audioManager.audioContext) {
                    window.audioManager.playSyntheticSound(this.src);
                } else {
                    console.log(`Playing: ${this.src}`);
                }
                this.paused = false;
                return Promise.resolve();
            },
            pause: function() {
                this.paused = true;
            },
            stop: function() {
                this.pause();
                this.currentTime = 0;
            }
        };
    }

    playSound(soundName) {
        if (this.muted || !this.sounds[soundName]) return;

        try {
            const sound = this.sounds[soundName];
            sound.currentTime = 0;
            sound.volume = this.soundVolume;
            sound.play();
        } catch (error) {
            console.warn(`Could not play sound: ${soundName}`);
        }
    }

    playMusic(musicName) {
        if (this.musicMuted || !this.music[musicName]) return;

        // Stop current music
        this.stopMusic();

        try {
            this.currentMusic = this.music[musicName];
            this.currentMusic.volume = this.musicVolume;
            this.currentMusic.loop = true;

            // Generate background music if Web Audio is available
            if (this.audioContext) {
                this.generateBackgroundMusic(musicName);
            } else {
                this.currentMusic.play();
            }
        } catch (error) {
            console.warn(`Could not play music: ${musicName}`);
        }
    }

    generateBackgroundMusic(musicType) {
        // Stop any existing background music
        if (this.backgroundMusicInterval) {
            clearInterval(this.backgroundMusicInterval);
        }

        // Generate simple background melodies based on music type
        switch (musicType) {
            case 'overworld':
                this.playOverworldMusic();
                break;
            case 'underground':
                this.playUndergroundMusic();
                break;
            case 'underwater':
                this.playUnderwaterMusic();
                break;
            case 'castle':
                this.playCastleMusic();
                break;
            case 'sky':
                this.playSkyMusic();
                break;
            case 'invincible':
                this.playInvincibleMusic();
                break;
            default:
                this.playOverworldMusic();
        }
    }

    playOverworldMusic() {
        // Happy upbeat melody
        const melody = [523, 523, 0, 523, 0, 415, 523, 0, 659]; // C C _ C _ G# C _ E
        let noteIndex = 0;

        this.backgroundMusicInterval = setInterval(() => {
            if (!this.musicMuted && this.audioContext) {
                const freq = melody[noteIndex];
                if (freq > 0) {
                    this.playMusicNote(freq, 0.3);
                }
                noteIndex = (noteIndex + 1) % melody.length;
            }
        }, 400);
    }

    playUndergroundMusic() {
        // Mysterious low melody
        const melody = [196, 220, 246, 220, 196, 174, 196]; // G A B A G F# G
        let noteIndex = 0;

        this.backgroundMusicInterval = setInterval(() => {
            if (!this.musicMuted && this.audioContext) {
                const freq = melody[noteIndex];
                this.playMusicNote(freq, 0.5);
                noteIndex = (noteIndex + 1) % melody.length;
            }
        }, 600);
    }

    playUnderwaterMusic() {
        // Flowing water-like melody
        const melody = [330, 370, 415, 370, 330, 294, 330, 370]; // E F# G# F# E D E F#
        let noteIndex = 0;

        this.backgroundMusicInterval = setInterval(() => {
            if (!this.musicMuted && this.audioContext) {
                const freq = melody[noteIndex];
                this.playMusicNote(freq, 0.4, 'sine');
                noteIndex = (noteIndex + 1) % melody.length;
            }
        }, 500);
    }

    playCastleMusic() {
        // Dark, ominous melody
        const melody = [147, 165, 175, 165, 147, 131, 147]; // D E F E D C D
        let noteIndex = 0;

        this.backgroundMusicInterval = setInterval(() => {
            if (!this.musicMuted && this.audioContext) {
                const freq = melody[noteIndex];
                this.playMusicNote(freq, 0.6, 'square');
                noteIndex = (noteIndex + 1) % melody.length;
            }
        }, 700);
    }

    playSkyMusic() {
        // Light, airy melody
        const melody = [659, 784, 880, 784, 659, 587, 659, 784]; // E G A G E D E G
        let noteIndex = 0;

        this.backgroundMusicInterval = setInterval(() => {
            if (!this.musicMuted && this.audioContext) {
                const freq = melody[noteIndex];
                this.playMusicNote(freq, 0.3, 'triangle');
                noteIndex = (noteIndex + 1) % melody.length;
            }
        }, 350);
    }

    playInvincibleMusic() {
        // Fast, energetic melody
        const melody = [523, 659, 784, 1047, 784, 659, 523, 659]; // C E G C G E C E
        let noteIndex = 0;

        this.backgroundMusicInterval = setInterval(() => {
            if (!this.musicMuted && this.audioContext) {
                const freq = melody[noteIndex];
                this.playMusicNote(freq, 0.2, 'sawtooth');
                noteIndex = (noteIndex + 1) % melody.length;
            }
        }, 200);
    }

    playMusicNote(frequency, duration, waveType = 'sine') {
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.type = waveType;
        oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);

        gainNode.gain.setValueAtTime(this.musicVolume * 0.3, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);

        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + duration);
    }

    stopMusic() {
        if (this.currentMusic) {
            this.currentMusic.stop();
            this.currentMusic = null;
        }

        // Stop background music generation
        if (this.backgroundMusicInterval) {
            clearInterval(this.backgroundMusicInterval);
            this.backgroundMusicInterval = null;
        }
    }

    pauseMusic() {
        if (this.currentMusic && !this.currentMusic.paused) {
            this.currentMusic.pause();
        }
    }

    resumeMusic() {
        if (this.currentMusic && this.currentMusic.paused && !this.musicMuted) {
            this.currentMusic.play();
        }
    }

    setMusicVolume(volume) {
        this.musicVolume = Math.max(0, Math.min(1, volume));

        Object.values(this.music).forEach(track => {
            track.volume = this.musicVolume;
        });

        if (this.currentMusic) {
            this.currentMusic.volume = this.musicVolume;
        }
    }

    setSoundVolume(volume) {
        this.soundVolume = Math.max(0, Math.min(1, volume));

        Object.values(this.sounds).forEach(sound => {
            sound.volume = this.soundVolume;
        });
    }

    toggleMute() {
        this.muted = !this.muted;
        if (this.muted) {
            this.setSoundVolume(0);
        } else {
            this.setSoundVolume(0.5);
        }
    }

    toggleMusicMute() {
        this.musicMuted = !this.musicMuted;
        if (this.musicMuted) {
            this.pauseMusic();
        } else {
            this.resumeMusic();
        }
    }

    // Game event sound mappings
    onMarioJump() {
        this.playSound('jump');
    }

    onCoinCollect() {
        this.playSound('coin');
    }

    onPowerUpCollect() {
        this.playSound('powerup');
    }

    onEnemyStomp() {
        this.playSound('stomp');
    }

    onBlockBreak() {
        this.playSound('break');
    }

    onMarioDeath() {
        this.stopMusic();
        this.playSound('death');
    }

    onGameOver() {
        this.stopMusic();
        this.playSound('gameOver');
    }

    onLevelComplete() {
        this.stopMusic();
        this.playSound('victory');
    }

    onFlagpoleTouch() {
        this.stopMusic();
        this.playSound('flagpole');
    }

    onOneUpGain() {
        this.playSound('oneUp');
    }

    onStarPowerActivate() {
        this.stopMusic();
        this.playMusic('invincible');
    }

    onStarPowerEnd() {
        this.stopMusic();
        // Resume level music based on current level
        this.resumeLevelMusic();
    }

    onFireballShoot() {
        this.playSound('fireball');
    }

    onPipeEnter() {
        this.playSound('pipe');
    }

    onShellKick() {
        this.playSound('kick');
    }

    onBlockBump() {
        this.playSound('bump');
    }

    // Level-specific music
    startLevelMusic(levelNumber) {
        switch (levelNumber) {
            case 1:
                this.playMusic('overworld');
                break;
            case 2:
                this.playMusic('underground');
                break;
            case 3:
                this.playMusic('underwater');
                break;
            case 4:
                this.playMusic('castle');
                break;
            case 5:
                this.playMusic('sky');
                break;
            default:
                this.playMusic('overworld');
        }
    }

    resumeLevelMusic() {
        // This would need to know the current level
        // For now, default to overworld
        this.playMusic('overworld');
    }

    // Audio context management for better performance
    createAudioContext() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            return true;
        } catch (error) {
            console.warn('Web Audio API not supported');
            return false;
        }
    }

    // Generate synthetic sounds using Web Audio API
    playSyntheticSound(soundName) {
        if (!this.audioContext || this.muted) return;

        const now = this.audioContext.currentTime;

        // Extract sound type from filename
        const soundType = soundName.split('/').pop().split('.')[0];

        switch (soundType) {
            case 'jump':
                this.generateJumpSound(now);
                break;
            case 'coin':
                this.generateCoinSound(now);
                break;
            case 'powerup':
                this.generatePowerUpSound(now);
                break;
            case 'fireball':
                this.generateFireballSound(now);
                break;
            case 'stomp':
                this.generateStompSound(now);
                break;
            case 'break':
                this.generateBreakSound(now);
                break;
            case 'death':
                this.generateDeathSound(now);
                break;
            case 'gameover':
                this.generateGameOverSound(now);
                break;
            case 'victory':
                this.generateVictorySound(now);
                break;
            case 'flagpole':
                this.generateFlagpoleSound(now);
                break;
            case '1up':
                this.generateOneUpSound(now);
                break;
            case 'star':
                this.generateStarSound(now);
                break;
            case 'pipe':
                this.generatePipeSound(now);
                break;
            case 'kick':
                this.generateKickSound(now);
                break;
            case 'bump':
                this.generateBumpSound(now);
                break;
            default:
                this.generateDefaultSound(now);
        }
    }

    // Individual sound generators
    generateJumpSound(startTime) {
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.frequency.setValueAtTime(400, startTime);
        oscillator.frequency.exponentialRampToValueAtTime(600, startTime + 0.1);

        gainNode.gain.setValueAtTime(0.3, startTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + 0.2);

        oscillator.start(startTime);
        oscillator.stop(startTime + 0.2);
    }

    generateCoinSound(startTime) {
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.frequency.setValueAtTime(800, startTime);
        oscillator.frequency.exponentialRampToValueAtTime(1200, startTime + 0.1);

        gainNode.gain.setValueAtTime(0.2, startTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + 0.15);

        oscillator.start(startTime);
        oscillator.stop(startTime + 0.15);
    }

    generatePowerUpSound(startTime) {
        // Multi-tone ascending sound
        const frequencies = [262, 330, 392, 523]; // C, E, G, C

        frequencies.forEach((freq, index) => {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            const noteStart = startTime + index * 0.1;
            oscillator.frequency.setValueAtTime(freq, noteStart);

            gainNode.gain.setValueAtTime(0.2, noteStart);
            gainNode.gain.exponentialRampToValueAtTime(0.01, noteStart + 0.15);

            oscillator.start(noteStart);
            oscillator.stop(noteStart + 0.15);
        });
    }

    generateFireballSound(startTime) {
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.frequency.setValueAtTime(200, startTime);
        oscillator.frequency.linearRampToValueAtTime(100, startTime + 0.1);

        gainNode.gain.setValueAtTime(0.3, startTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + 0.1);

        oscillator.start(startTime);
        oscillator.stop(startTime + 0.1);
    }

    generateStompSound(startTime) {
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.frequency.setValueAtTime(150, startTime);
        oscillator.frequency.exponentialRampToValueAtTime(50, startTime + 0.1);

        gainNode.gain.setValueAtTime(0.4, startTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + 0.1);

        oscillator.start(startTime);
        oscillator.stop(startTime + 0.1);
    }

    generateBreakSound(startTime) {
        // Multiple noise bursts for breaking sound
        for (let i = 0; i < 3; i++) {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            oscillator.type = 'square';
            oscillator.frequency.setValueAtTime(200 + i * 50, startTime + i * 0.02);

            gainNode.gain.setValueAtTime(0.2, startTime + i * 0.02);
            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + i * 0.02 + 0.05);

            oscillator.start(startTime + i * 0.02);
            oscillator.stop(startTime + i * 0.02 + 0.05);
        }
    }

    generateDeathSound(startTime) {
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.frequency.setValueAtTime(400, startTime);
        oscillator.frequency.exponentialRampToValueAtTime(100, startTime + 1);

        gainNode.gain.setValueAtTime(0.3, startTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + 1);

        oscillator.start(startTime);
        oscillator.stop(startTime + 1);
    }

    generateGameOverSound(startTime) {
        // Descending sad melody
        const frequencies = [523, 392, 330, 262]; // C, G, E, C

        frequencies.forEach((freq, index) => {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            const noteStart = startTime + index * 0.3;
            oscillator.frequency.setValueAtTime(freq, noteStart);

            gainNode.gain.setValueAtTime(0.2, noteStart);
            gainNode.gain.exponentialRampToValueAtTime(0.01, noteStart + 0.4);

            oscillator.start(noteStart);
            oscillator.stop(noteStart + 0.4);
        });
    }

    generateVictorySound(startTime) {
        // Happy ascending melody
        const frequencies = [262, 330, 392, 523, 659]; // C, E, G, C, E

        frequencies.forEach((freq, index) => {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            const noteStart = startTime + index * 0.15;
            oscillator.frequency.setValueAtTime(freq, noteStart);

            gainNode.gain.setValueAtTime(0.2, noteStart);
            gainNode.gain.exponentialRampToValueAtTime(0.01, noteStart + 0.2);

            oscillator.start(noteStart);
            oscillator.stop(noteStart + 0.2);
        });
    }

    generateFlagpoleSound(startTime) {
        // Sliding down sound
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.frequency.setValueAtTime(800, startTime);
        oscillator.frequency.exponentialRampToValueAtTime(200, startTime + 2);

        gainNode.gain.setValueAtTime(0.2, startTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + 2);

        oscillator.start(startTime);
        oscillator.stop(startTime + 2);
    }

    generateOneUpSound(startTime) {
        // Special 1-up melody
        const frequencies = [659, 831, 1047, 1319]; // E, G#, C, E

        frequencies.forEach((freq, index) => {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            const noteStart = startTime + index * 0.1;
            oscillator.frequency.setValueAtTime(freq, noteStart);

            gainNode.gain.setValueAtTime(0.2, noteStart);
            gainNode.gain.exponentialRampToValueAtTime(0.01, noteStart + 0.15);

            oscillator.start(noteStart);
            oscillator.stop(noteStart + 0.15);
        });
    }

    generateStarSound(startTime) {
        // Sparkling star sound
        for (let i = 0; i < 5; i++) {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();

            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);

            oscillator.frequency.setValueAtTime(800 + i * 200, startTime + i * 0.05);

            gainNode.gain.setValueAtTime(0.1, startTime + i * 0.05);
            gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + i * 0.05 + 0.1);

            oscillator.start(startTime + i * 0.05);
            oscillator.stop(startTime + i * 0.05 + 0.1);
        }
    }

    generatePipeSound(startTime) {
        // Whoosh sound
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.type = 'sawtooth';
        oscillator.frequency.setValueAtTime(300, startTime);
        oscillator.frequency.linearRampToValueAtTime(100, startTime + 0.5);

        gainNode.gain.setValueAtTime(0.3, startTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + 0.5);

        oscillator.start(startTime);
        oscillator.stop(startTime + 0.5);
    }

    generateKickSound(startTime) {
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.type = 'square';
        oscillator.frequency.setValueAtTime(120, startTime);

        gainNode.gain.setValueAtTime(0.3, startTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + 0.1);

        oscillator.start(startTime);
        oscillator.stop(startTime + 0.1);
    }

    generateBumpSound(startTime) {
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.type = 'triangle';
        oscillator.frequency.setValueAtTime(250, startTime);
        oscillator.frequency.exponentialRampToValueAtTime(150, startTime + 0.1);

        gainNode.gain.setValueAtTime(0.2, startTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + 0.1);

        oscillator.start(startTime);
        oscillator.stop(startTime + 0.1);
    }

    generateDefaultSound(startTime) {
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.frequency.setValueAtTime(440, startTime);

        gainNode.gain.setValueAtTime(0.1, startTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, startTime + 0.1);

        oscillator.start(startTime);
        oscillator.stop(startTime + 0.1);
    }

    // Preload audio files
    preloadAudio() {
        const audioPromises = [];

        // Preload sounds
        Object.values(this.sounds).forEach(sound => {
            if (sound.src) {
                audioPromises.push(this.loadAudio(sound.src));
            }
        });

        // Preload music
        Object.values(this.music).forEach(track => {
            if (track.src) {
                audioPromises.push(this.loadAudio(track.src));
            }
        });

        return Promise.all(audioPromises);
    }

    loadAudio(src) {
        return new Promise((resolve, reject) => {
            // In a real implementation, this would load the actual audio file
            console.log(`Loading audio: ${src}`);
            setTimeout(() => resolve(src), 100); // Simulate loading
        });
    }

    // Audio effects
    playRandomPitchSound(soundName, pitchVariation = 0.2) {
        if (this.muted || !this.sounds[soundName]) return;

        const sound = this.sounds[soundName];
        const pitch = 1 + (Math.random() - 0.5) * pitchVariation;

        // In a real implementation, this would modify the playback rate
        console.log(`Playing ${soundName} with pitch: ${pitch}`);
        this.playSound(soundName);
    }

    fadeOutMusic(duration = 1000) {
        if (!this.currentMusic) return;

        const startVolume = this.currentMusic.volume;
        const fadeStep = startVolume / (duration / 50);

        const fadeInterval = setInterval(() => {
            if (this.currentMusic.volume > fadeStep) {
                this.currentMusic.volume -= fadeStep;
            } else {
                this.currentMusic.volume = 0;
                this.stopMusic();
                clearInterval(fadeInterval);
            }
        }, 50);
    }

    fadeInMusic(musicName, duration = 1000) {
        this.playMusic(musicName);
        if (!this.currentMusic) return;

        this.currentMusic.volume = 0;
        const targetVolume = this.musicVolume;
        const fadeStep = targetVolume / (duration / 50);

        const fadeInterval = setInterval(() => {
            if (this.currentMusic.volume < targetVolume - fadeStep) {
                this.currentMusic.volume += fadeStep;
            } else {
                this.currentMusic.volume = targetVolume;
                clearInterval(fadeInterval);
            }
        }, 50);
    }

    // Cleanup
    destroy() {
        this.stopMusic();
        Object.values(this.sounds).forEach(sound => {
            if (sound.stop) sound.stop();
        });
        Object.values(this.music).forEach(track => {
            if (track.stop) track.stop();
        });

        if (this.audioContext) {
            this.audioContext.close();
        }
    }
}

// Export for use in other files
window.AudioManager = AudioManager;
