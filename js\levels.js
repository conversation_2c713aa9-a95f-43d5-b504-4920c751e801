// Level Definitions for Super Mario Game

class LevelManager {
    constructor() {
        this.currentLevel = 1;
        this.totalLevels = 5;
        this.levels = {};
        this.initializeLevels();
    }
    
    initializeLevels() {
        this.levels[1] = new Level1();
        this.levels[2] = new Level2();
        this.levels[3] = new Level3();
        this.levels[4] = new Level4();
        this.levels[5] = new Level5();
    }
    
    getCurrentLevel() {
        return this.levels[this.currentLevel];
    }
    
    nextLevel() {
        if (this.currentLevel < this.totalLevels) {
            this.currentLevel++;
            return this.getCurrentLevel();
        }
        return null; // Game complete
    }
    
    resetToLevel(levelNumber) {
        if (levelNumber >= 1 && levelNumber <= this.totalLevels) {
            this.currentLevel = levelNumber;
            return this.getCurrentLevel();
        }
        return null;
    }
    
    isLastLevel() {
        return this.currentLevel === this.totalLevels;
    }
}

// Base Level Class
class Level {
    constructor(name, theme, timeLimit = 400) {
        this.name = name;
        this.theme = theme;
        this.timeLimit = timeLimit;
        this.width = 3200; // Level width in pixels
        this.height = 600; // Level height in pixels
        this.startX = 100;
        this.startY = 400;
        this.blocks = [];
        this.enemies = [];
        this.coins = [];
        this.powerUps = [];
        this.backgroundClass = '';
        this.music = '';
        this.completed = false;
    }
    
    reset() {
        this.completed = false;
        this.initializeLevel();
    }
    
    initializeLevel() {
        // Override in subclasses
    }
    
    getStartPosition() {
        return { x: this.startX, y: this.startY };
    }
    
    getBlocks() {
        return this.blocks;
    }
    
    getEnemies() {
        return this.enemies;
    }
    
    getCoins() {
        return this.coins;
    }
    
    getPowerUps() {
        return this.powerUps;
    }
    
    getBackgroundClass() {
        return this.backgroundClass;
    }
}

// Level 1: Grassland Adventure
class Level1 extends Level {
    constructor() {
        super("المرحلة الأولى: مغامرة الأراضي الخضراء", "grassland", 400);
        this.backgroundClass = 'level-1';
        this.music = 'sounds/level1.mp3';
        this.initializeLevel();
    }
    
    initializeLevel() {
        this.blocks = [];
        this.enemies = [];
        this.coins = [];
        this.powerUps = [];
        
        // Ground blocks
        for (let x = 0; x < this.width; x += 32) {
            this.blocks.push(new GroundBlock(x, 568));
        }
        
        // Platforms and obstacles
        this.createPlatforms();
        this.createQuestionBlocks();
        this.createBricks();
        this.createPipes();
        this.createEnemies();
        this.createCoins();
        this.createGoal();
    }
    
    createPlatforms() {
        // Small platforms
        for (let x = 400; x <= 464; x += 32) {
            this.blocks.push(new GroundBlock(x, 450));
        }
        
        for (let x = 600; x <= 696; x += 32) {
            this.blocks.push(new GroundBlock(x, 400));
        }
        
        for (let x = 900; x <= 996; x += 32) {
            this.blocks.push(new GroundBlock(x, 350));
        }
        
        // Moving platform
        this.blocks.push(new MovingPlatform(1200, 400, 96, { type: 'horizontal', distance: 100, speed: 1 }));
    }
    
    createQuestionBlocks() {
        this.blocks.push(new QuestionBlock(300, 400, 'coin'));
        this.blocks.push(new QuestionBlock(500, 300, 'powerup'));
        this.blocks.push(new QuestionBlock(800, 250, 'coin'));
        this.blocks.push(new QuestionBlock(1000, 200, '1up'));
        this.blocks.push(new QuestionBlock(1500, 350, 'star'));
    }
    
    createBricks() {
        // Brick formations
        for (let x = 350; x <= 414; x += 32) {
            this.blocks.push(new BrickBlock(x, 400, x === 382 ? 'coin' : null));
        }
        
        for (let x = 750; x <= 814; x += 32) {
            this.blocks.push(new BrickBlock(x, 250, x === 782 ? 'powerup' : null));
        }
    }
    
    createPipes() {
        this.blocks.push(new Pipe(1800, 504, 64));
        this.blocks.push(new Pipe(2200, 472, 96));
        this.blocks.push(new Pipe(2600, 440, 128));
    }
    
    createEnemies() {
        this.enemies.push(new Goomba(350, 536));
        this.enemies.push(new Goomba(550, 536));
        this.enemies.push(new Koopa(750, 520));
        this.enemies.push(new Goomba(950, 536));
        this.enemies.push(new Koopa(1150, 520));
        this.enemies.push(new Goomba(1400, 536));
        this.enemies.push(new Goomba(1600, 536));
        this.enemies.push(new Koopa(1900, 520));
        this.enemies.push(new Goomba(2100, 536));
        this.enemies.push(new Koopa(2400, 520));
    }
    
    createCoins() {
        // Coin trails
        for (let x = 450; x <= 550; x += 25) {
            this.coins.push(new Coin(x, 350));
        }
        
        for (let x = 650; x <= 750; x += 25) {
            this.coins.push(new Coin(x, 300));
        }
        
        // Coin rings
        this.coins.push(new CoinRing(1100, 250, 50, 6));
        
        // Hidden coins
        for (let x = 1700; x <= 1800; x += 50) {
            this.coins.push(new Coin(x, 200));
        }
    }
    
    createGoal() {
        this.blocks.push(new FlagPole(3000, 248));
        this.blocks.push(new CastleBlock(3100, 440));
    }
}

// Level 2: Underground Caverns
class Level2 extends Level {
    constructor() {
        super("المرحلة الثانية: كهوف تحت الأرض", "underground", 350);
        this.backgroundClass = 'level-2';
        this.music = 'sounds/level2.mp3';
        this.initializeLevel();
    }
    
    initializeLevel() {
        this.blocks = [];
        this.enemies = [];
        this.coins = [];
        this.powerUps = [];
        
        // Ground and ceiling
        for (let x = 0; x < this.width; x += 32) {
            this.blocks.push(new GroundBlock(x, 568));
            this.blocks.push(new GroundBlock(x, 0));
        }
        
        this.createCavePlatforms();
        this.createPipes();
        this.createEnemies();
        this.createCoins();
        this.createGoal();
    }
    
    createCavePlatforms() {
        // Stalactites and stalagmites
        for (let x = 200; x < this.width; x += 150) {
            const height = 64 + Math.random() * 96;
            this.blocks.push(new GroundBlock(x, 32));
            this.blocks.push(new GroundBlock(x, 568 - height));
        }
        
        // Narrow passages
        for (let x = 800; x <= 1000; x += 32) {
            this.blocks.push(new GroundBlock(x, 300));
            this.blocks.push(new GroundBlock(x, 200));
        }
        
        // Question blocks in tight spaces
        this.blocks.push(new QuestionBlock(450, 450, 'fireflower'));
        this.blocks.push(new QuestionBlock(850, 150, 'coin'));
        this.blocks.push(new QuestionBlock(1200, 400, 'powerup'));
    }
    
    createPipes() {
        // Piranha plant pipes
        this.blocks.push(new Pipe(600, 504, 64));
        this.enemies.push(new PiranhaPlant(616, 456));
        
        this.blocks.push(new Pipe(1400, 504, 64));
        this.enemies.push(new PiranhaPlant(1416, 456));
        
        this.blocks.push(new Pipe(2000, 504, 64));
        this.enemies.push(new PiranhaPlant(2016, 456));
    }
    
    createEnemies() {
        // Underground enemies
        this.enemies.push(new Koopa(300, 520));
        this.enemies.push(new Goomba(500, 536));
        this.enemies.push(new Koopa(900, 520));
        this.enemies.push(new Goomba(1100, 536));
        this.enemies.push(new Koopa(1600, 520));
        this.enemies.push(new Goomba(1800, 536));
        this.enemies.push(new Koopa(2200, 520));
        this.enemies.push(new Goomba(2400, 536));
    }
    
    createCoins() {
        // Coins in narrow passages
        for (let x = 250; x < this.width; x += 100) {
            this.coins.push(new Coin(x, 100));
            this.coins.push(new Coin(x + 50, 500));
        }
        
        // Red coins in secret areas
        this.coins.push(new RedCoin(750, 250));
        this.coins.push(new RedCoin(1350, 250));
        this.coins.push(new RedCoin(1950, 250));
    }
    
    createGoal() {
        this.blocks.push(new FlagPole(2900, 248));
        this.blocks.push(new CastleBlock(3000, 440));
    }
}

// Level 3: Underwater Adventure
class Level3 extends Level {
    constructor() {
        super("المرحلة الثالثة: مغامرة تحت الماء", "underwater", 300);
        this.backgroundClass = 'level-3';
        this.music = 'sounds/level3.mp3';
        this.initializeLevel();
    }
    
    initializeLevel() {
        this.blocks = [];
        this.enemies = [];
        this.coins = [];
        this.powerUps = [];
        
        // Underwater physics will be different
        this.createCoralReefs();
        this.createSeaweedPipes();
        this.createEnemies();
        this.createCoins();
        this.createGoal();
    }
    
    createCoralReefs() {
        // Coral formations
        for (let x = 200; x < this.width; x += 200) {
            const height = 100 + Math.random() * 100;
            for (let y = 568 - height; y <= 568; y += 32) {
                this.blocks.push(new GroundBlock(x, y));
            }
        }
        
        // Floating platforms
        this.blocks.push(new CloudPlatform(400, 300));
        this.blocks.push(new CloudPlatform(800, 200));
        this.blocks.push(new CloudPlatform(1200, 350));
        this.blocks.push(new CloudPlatform(1600, 250));
    }
    
    createSeaweedPipes() {
        this.blocks.push(new Pipe(600, 504, 64));
        this.blocks.push(new Pipe(1000, 472, 96));
        this.blocks.push(new Pipe(1400, 440, 128));
        this.blocks.push(new Pipe(1800, 504, 64));
    }
    
    createEnemies() {
        // Underwater enemies (modified behavior)
        this.enemies.push(new Koopa(350, 520, 'red')); // Red Koopa swims
        this.enemies.push(new Goomba(550, 536));
        this.enemies.push(new Koopa(750, 520, 'red'));
        this.enemies.push(new Goomba(950, 536));
        this.enemies.push(new Koopa(1150, 520, 'red'));
        this.enemies.push(new Goomba(1350, 536));
    }
    
    createCoins() {
        // Underwater coin formations
        for (let x = 300; x < this.width; x += 150) {
            for (let y = 100; y <= 400; y += 75) {
                this.coins.push(new Coin(x, y));
            }
        }
        
        // Blue coins (special underwater coins)
        this.coins.push(new BlueCoin(500, 150));
        this.coins.push(new BlueCoin(900, 100));
        this.coins.push(new BlueCoin(1300, 175));
    }
    
    createGoal() {
        this.blocks.push(new FlagPole(2800, 248));
        this.blocks.push(new CastleBlock(2900, 440));
    }
}

// Level 4: Castle Fortress
class Level4 extends Level {
    constructor() {
        super("المرحلة الرابعة: قلعة الحصن", "castle", 250);
        this.backgroundClass = 'level-4';
        this.music = 'sounds/level4.mp3';
        this.initializeLevel();
    }
    
    initializeLevel() {
        this.blocks = [];
        this.enemies = [];
        this.coins = [];
        this.powerUps = [];
        
        this.createCastleStructure();
        this.createLavaPits();
        this.createEnemies();
        this.createCoins();
        this.createBossFight();
    }
    
    createCastleStructure() {
        // Castle walls and platforms
        for (let x = 0; x < this.width; x += 32) {
            this.blocks.push(new GroundBlock(x, 568));
        }
        
        // Castle platforms
        for (let x = 300; x <= 500; x += 32) {
            this.blocks.push(new GroundBlock(x, 400));
        }
        
        for (let x = 700; x <= 900; x += 32) {
            this.blocks.push(new GroundBlock(x, 300));
        }
        
        // Moving platforms over lava
        this.blocks.push(new MovingPlatform(1100, 450, 64, { type: 'vertical', distance: 100, speed: 2 }));
        this.blocks.push(new MovingPlatform(1300, 350, 64, { type: 'horizontal', distance: 150, speed: 1.5 }));
    }
    
    createLavaPits() {
        // Lava blocks
        for (let x = 600; x <= 696; x += 32) {
            this.blocks.push(new LavaBlock(x, 536));
        }
        
        for (let x = 1000; x <= 1200; x += 32) {
            this.blocks.push(new LavaBlock(x, 536));
        }
    }
    
    createEnemies() {
        // Castle enemies
        this.enemies.push(new Koopa(400, 368, 'red'));
        this.enemies.push(new HammerBro(800, 252));
        this.enemies.push(new Koopa(1500, 520, 'red'));
        this.enemies.push(new HammerBro(1700, 520));
        this.enemies.push(new Lakitu(2000, 200));
    }
    
    createCoins() {
        // Dangerous coin placements
        for (let x = 620; x <= 680; x += 30) {
            this.coins.push(new Coin(x, 450)); // Above lava
        }
        
        this.coins.push(new RedCoin(850, 200));
        this.coins.push(new RedCoin(1150, 300));
        this.coins.push(new RedCoin(1550, 400));
    }
    
    createBossFight() {
        // Boss area
        this.blocks.push(new CastleBlock(2800, 440));
        // Boss will be handled separately in game logic
    }
}

// Level 5: Sky Kingdom
class Level5 extends Level {
    constructor() {
        super("المرحلة الخامسة: مملكة السماء", "sky", 400);
        this.backgroundClass = 'level-5';
        this.music = 'sounds/level5.mp3';
        this.initializeLevel();
    }
    
    initializeLevel() {
        this.blocks = [];
        this.enemies = [];
        this.coins = [];
        this.powerUps = [];
        
        this.createCloudPlatforms();
        this.createSkyEnemies();
        this.createCoins();
        this.createFinalGoal();
    }
    
    createCloudPlatforms() {
        // Floating cloud platforms
        this.blocks.push(new CloudPlatform(200, 500));
        this.blocks.push(new CloudPlatform(400, 400));
        this.blocks.push(new CloudPlatform(600, 300));
        this.blocks.push(new CloudPlatform(800, 450));
        this.blocks.push(new CloudPlatform(1000, 350));
        this.blocks.push(new CloudPlatform(1200, 250));
        this.blocks.push(new CloudPlatform(1400, 400));
        this.blocks.push(new CloudPlatform(1600, 300));
        this.blocks.push(new CloudPlatform(1800, 200));
        this.blocks.push(new CloudPlatform(2000, 350));
        
        // Moving cloud platforms
        this.blocks.push(new MovingPlatform(2200, 400, 96, { type: 'circular', radius: 80, speed: 2 }));
        this.blocks.push(new MovingPlatform(2500, 300, 96, { type: 'vertical', distance: 150, speed: 1 }));
    }
    
    createSkyEnemies() {
        // Sky enemies
        this.enemies.push(new Lakitu(500, 150));
        this.enemies.push(new Lakitu(900, 100));
        this.enemies.push(new Lakitu(1300, 120));
        this.enemies.push(new Lakitu(1700, 80));
        this.enemies.push(new Lakitu(2100, 150));
        
        // Bullet Bills from off-screen
        setInterval(() => {
            this.enemies.push(new BulletBill(this.width + 50, 300 + Math.random() * 200, -1));
        }, 5000);
    }
    
    createCoins() {
        // Sky coin formations
        this.coins.push(new CoinRing(700, 200, 60, 8));
        this.coins.push(new CoinRing(1100, 150, 70, 10));
        this.coins.push(new CoinRing(1500, 100, 80, 12));
        
        // Rainbow coin trail
        for (let i = 0; i < 20; i++) {
            const x = 1800 + i * 30;
            const y = 100 + Math.sin(i * 0.5) * 50;
            this.coins.push(new Coin(x, y));
        }
    }
    
    createFinalGoal() {
        this.blocks.push(new CloudPlatform(2800, 300));
        this.blocks.push(new FlagPole(2850, -20));
        this.blocks.push(new CastleBlock(2900, 172));
    }
}

// Export classes
window.LevelManager = LevelManager;
window.Level = Level;
window.Level1 = Level1;
window.Level2 = Level2;
window.Level3 = Level3;
window.Level4 = Level4;
window.Level5 = Level5;
