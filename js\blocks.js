// Block Classes for Super Mario Game

// Base Block Class
class Block {
    constructor(x, y, width = 32, height = 32) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
        this.solid = true;
        this.type = 'block';
        this.destroyed = false;
        this.animationFrame = 0;
        this.animationTimer = 0;
    }
    
    update(deltaTime) {
        this.updateAnimation(deltaTime);
    }
    
    updateAnimation(deltaTime) {
        this.animationTimer += deltaTime;
        if (this.animationTimer >= 200) {
            this.animationFrame = (this.animationFrame + 1) % 4;
            this.animationTimer = 0;
        }
    }
    
    onHit(mario, side) {
        // Override in subclasses
        return false;
    }
    
    getBounds() {
        return {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height
        };
    }
    
    getSpriteClass() {
        return this.type + '-block';
    }
}

// Ground Block
class GroundBlock extends Block {
    constructor(x, y) {
        super(x, y);
        this.type = 'ground';
        this.breakable = false;
    }
}

// Brick Block
class BrickBlock extends Block {
    constructor(x, y, contents = null) {
        super(x, y);
        this.type = 'brick';
        this.breakable = true;
        this.contents = contents;
        this.breaking = false;
    }
    
    onHit(mario, side) {
        if (side === 'bottom') {
            if (mario.state === 'small' && this.contents) {
                // Small Mario can't break bricks, but can get contents
                this.releaseContents();
                return true;
            } else if (mario.state !== 'small') {
                // Super/Fire Mario can break bricks
                this.break();
                return true;
            }
        }
        return false;
    }
    
    break() {
        this.breaking = true;
        this.solid = false;
        
        // Create break particles
        this.createBreakParticles();
        
        setTimeout(() => {
            this.destroyed = true;
        }, 300);
        
        return 50; // Score points
    }
    
    releaseContents() {
        if (this.contents) {
            const item = this.createItem(this.contents);
            this.contents = null;
            return item;
        }
        return null;
    }
    
    createItem(itemType) {
        switch (itemType) {
            case 'coin':
                return new Coin(this.x, this.y - 32);
            case 'powerup':
                return new SuperMushroom(this.x, this.y - 32);
            case 'fireflower':
                return new FireFlower(this.x, this.y - 32);
            case '1up':
                return new OneUpMushroom(this.x, this.y - 32);
            default:
                return null;
        }
    }
    
    createBreakParticles() {
        // This would create visual break particles
        // Implementation depends on particle system
    }
    
    getSpriteClass() {
        let classes = [this.type + '-block'];
        if (this.breaking) classes.push('breaking');
        return classes.join(' ');
    }
}

// Question Block
class QuestionBlock extends Block {
    constructor(x, y, contents = 'coin') {
        super(x, y);
        this.type = 'question';
        this.contents = contents;
        this.used = false;
        this.bouncing = false;
    }
    
    onHit(mario, side) {
        if (side === 'bottom' && !this.used) {
            this.used = true;
            this.bouncing = true;
            
            setTimeout(() => {
                this.bouncing = false;
            }, 300);
            
            const item = this.releaseContents();
            return item;
        }
        return false;
    }
    
    releaseContents() {
        if (this.contents) {
            const item = this.createItem(this.contents);
            this.contents = null;
            return item;
        }
        return null;
    }
    
    createItem(itemType) {
        switch (itemType) {
            case 'coin':
                return new Coin(this.x, this.y - 32);
            case 'powerup':
                return new SuperMushroom(this.x, this.y - 32);
            case 'fireflower':
                return new FireFlower(this.x, this.y - 32);
            case '1up':
                return new OneUpMushroom(this.x, this.y - 32);
            case 'star':
                return new StarPower(this.x, this.y - 32);
            default:
                return new Coin(this.x, this.y - 32);
        }
    }
    
    getSpriteClass() {
        let classes = [this.type + '-block'];
        if (this.used) classes.push('used');
        if (this.bouncing) classes.push('hit');
        return classes.join(' ');
    }
}

// Pipe
class Pipe extends Block {
    constructor(x, y, height = 64, warpDestination = null) {
        super(x, y, 64, height);
        this.type = 'pipe';
        this.warpDestination = warpDestination;
        this.canWarp = warpDestination !== null;
    }
    
    onHit(mario, side) {
        if (this.canWarp && side === 'top' && mario.keys.down) {
            // Trigger warp
            return { type: 'warp', destination: this.warpDestination };
        }
        return false;
    }
    
    getSpriteClass() {
        let classes = [this.type];
        if (this.height === 64) classes.push('small');
        else if (this.height === 96) classes.push('medium');
        else classes.push('large');
        return classes.join(' ');
    }
}

// Moving Platform
class MovingPlatform extends Block {
    constructor(x, y, width = 96, path = null) {
        super(x, y, width, 16);
        this.type = 'moving-platform';
        this.startX = x;
        this.startY = y;
        this.path = path || { type: 'horizontal', distance: 100, speed: 1 };
        this.pathProgress = 0;
        this.direction = 1;
    }
    
    update(deltaTime) {
        super.update(deltaTime);
        this.updateMovement(deltaTime);
    }
    
    updateMovement(deltaTime) {
        switch (this.path.type) {
            case 'horizontal':
                this.pathProgress += this.direction * this.path.speed;
                if (this.pathProgress >= this.path.distance || this.pathProgress <= 0) {
                    this.direction *= -1;
                }
                this.x = this.startX + this.pathProgress;
                break;
                
            case 'vertical':
                this.pathProgress += this.direction * this.path.speed;
                if (this.pathProgress >= this.path.distance || this.pathProgress <= 0) {
                    this.direction *= -1;
                }
                this.y = this.startY + this.pathProgress;
                break;
                
            case 'circular':
                this.pathProgress += this.path.speed * 0.02;
                this.x = this.startX + Math.cos(this.pathProgress) * this.path.radius;
                this.y = this.startY + Math.sin(this.pathProgress) * this.path.radius;
                break;
        }
    }
}

// Cloud Platform
class CloudPlatform extends Block {
    constructor(x, y) {
        super(x, y, 96, 32);
        this.type = 'cloud-platform';
        this.fallThrough = true; // Can jump through from below
    }
    
    onHit(mario, side) {
        if (side === 'top') {
            return true; // Allow standing on top
        } else if (side === 'bottom' && mario.keys.down) {
            return false; // Allow falling through when down is pressed
        }
        return this.fallThrough ? false : true;
    }
}

// Invisible Block
class InvisibleBlock extends Block {
    constructor(x, y, contents = 'coin') {
        super(x, y);
        this.type = 'invisible';
        this.contents = contents;
        this.revealed = false;
        this.solid = false;
    }
    
    onHit(mario, side) {
        if (side === 'bottom' && !this.revealed) {
            this.revealed = true;
            this.solid = true;
            
            const item = this.releaseContents();
            return item;
        }
        return false;
    }
    
    releaseContents() {
        if (this.contents) {
            const item = this.createItem(this.contents);
            this.contents = null;
            return item;
        }
        return null;
    }
    
    createItem(itemType) {
        switch (itemType) {
            case 'coin':
                return new Coin(this.x, this.y - 32);
            case '1up':
                return new OneUpMushroom(this.x, this.y - 32);
            default:
                return new Coin(this.x, this.y - 32);
        }
    }
    
    getSpriteClass() {
        let classes = [this.type + '-block'];
        if (this.revealed) classes.push('revealed');
        return classes.join(' ');
    }
}

// Lava Block
class LavaBlock extends Block {
    constructor(x, y) {
        super(x, y);
        this.type = 'lava';
        this.deadly = true;
    }
    
    onHit(mario, side) {
        // Touching lava causes damage
        mario.takeDamage();
        return false;
    }
}

// Castle Block
class CastleBlock extends Block {
    constructor(x, y, width = 160, height = 128) {
        super(x, y, width, height);
        this.type = 'castle';
        this.isGoal = true;
    }
    
    onHit(mario, side) {
        if (side === 'left') {
            // Mario reached the castle
            return { type: 'levelComplete' };
        }
        return false;
    }
}

// Flag Pole
class FlagPole extends Block {
    constructor(x, y) {
        super(x, y, 8, 320);
        this.type = 'flag-pole';
        this.flag = new Flag(x + 8, y);
        this.flagHeight = 0; // Flag starts at top
        this.touched = false;
    }
    
    update(deltaTime) {
        super.update(deltaTime);
        if (this.touched && this.flagHeight < 280) {
            this.flagHeight += 2; // Flag slides down
            this.flag.y = this.y + this.flagHeight;
        }
    }
    
    onHit(mario, side) {
        if (!this.touched) {
            this.touched = true;
            // Calculate score based on flag height
            const score = Math.max(100, 5000 - (this.flagHeight * 10));
            return { type: 'flagTouch', score: score };
        }
        return false;
    }
}

// Flag
class Flag {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 32;
        this.height = 24;
    }
    
    getSpriteClass() {
        return 'flag';
    }
    
    getBounds() {
        return {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height
        };
    }
}

// Export classes
window.Block = Block;
window.GroundBlock = GroundBlock;
window.BrickBlock = BrickBlock;
window.QuestionBlock = QuestionBlock;
window.Pipe = Pipe;
window.MovingPlatform = MovingPlatform;
window.CloudPlatform = CloudPlatform;
window.InvisibleBlock = InvisibleBlock;
window.LavaBlock = LavaBlock;
window.CastleBlock = CastleBlock;
window.FlagPole = FlagPole;
window.Flag = Flag;
