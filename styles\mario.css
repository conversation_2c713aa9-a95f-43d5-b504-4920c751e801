/* Mario Character Styles */
.mario {
    position: absolute;
    width: 32px;
    height: 32px;
    transition: all 0.1s ease;
    z-index: 50;
}

/* Small Mario */
.mario.small {
    width: 32px;
    height: 32px;
}

/* Super Mario */
.mario.super {
    width: 32px;
    height: 48px;
}

/* Fire Mario */
.mario.fire {
    width: 32px;
    height: 48px;
}

/* Mario Sprite - Small Mario */
.mario.small .mario-sprite {
    width: 32px;
    height: 32px;
    background: 
        /* Hat */
        radial-gradient(circle at 16px 8px, #FF0000 0%, #CC0000 70%, transparent 70%),
        /* Face */
        radial-gradient(circle at 16px 16px, #FFDBAC 0%, #FFDBAC 60%, transparent 60%),
        /* Shirt */
        radial-gradient(circle at 16px 24px, #0066FF 0%, #0044CC 70%, transparent 70%);
    position: relative;
    border-radius: 2px;
}

/* <PERSON> Details */
.mario.small .mario-sprite::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        /* Eyes */
        radial-gradient(circle 2px at 12px 14px, #000 0%, #000 100%, transparent 100%),
        radial-gradient(circle 2px at 20px 14px, #000 0%, #000 100%, transparent 100%),
        /* Mustache */
        ellipse(8px 3px) at 16px 18px,
        /* Buttons */
        radial-gradient(circle 1px at 16px 22px, #FFD700 0%, #FFD700 100%, transparent 100%);
    background-color: transparent;
}

/* Super Mario Sprite */
.mario.super .mario-sprite {
    width: 32px;
    height: 48px;
    background: 
        /* Hat */
        radial-gradient(circle at 16px 8px, #FF0000 0%, #CC0000 70%, transparent 70%),
        /* Face */
        radial-gradient(circle at 16px 20px, #FFDBAC 0%, #FFDBAC 60%, transparent 60%),
        /* Shirt */
        radial-gradient(circle at 16px 32px, #0066FF 0%, #0044CC 70%, transparent 70%),
        /* Overalls */
        radial-gradient(circle at 16px 40px, #8B4513 0%, #654321 70%, transparent 70%);
    position: relative;
    border-radius: 2px;
}

/* Fire Mario Sprite */
.mario.fire .mario-sprite {
    width: 32px;
    height: 48px;
    background: 
        /* Hat */
        radial-gradient(circle at 16px 8px, #FF0000 0%, #CC0000 70%, transparent 70%),
        /* Face */
        radial-gradient(circle at 16px 20px, #FFDBAC 0%, #FFDBAC 60%, transparent 60%),
        /* Fire Shirt */
        radial-gradient(circle at 16px 32px, #FF4500 0%, #FF6347 70%, transparent 70%),
        /* Overalls */
        radial-gradient(circle at 16px 40px, #8B4513 0%, #654321 70%, transparent 70%);
    position: relative;
    border-radius: 2px;
    animation: fireGlow 1s ease-in-out infinite alternate;
}

@keyframes fireGlow {
    from { box-shadow: 0 0 5px #FF4500; }
    to { box-shadow: 0 0 15px #FF4500, 0 0 25px #FF6347; }
}

/* Mario Movement Animations */
.mario.walking {
    animation: walk 0.3s steps(2) infinite;
}

@keyframes walk {
    0% { transform: translateX(0); }
    50% { transform: translateX(1px); }
    100% { transform: translateX(0); }
}

.mario.jumping {
    animation: jump 0.5s ease-out;
}

@keyframes jump {
    0% { transform: translateY(0) rotate(0deg); }
    50% { transform: translateY(-20px) rotate(-5deg); }
    100% { transform: translateY(0) rotate(0deg); }
}

.mario.running {
    animation: run 0.2s steps(3) infinite;
}

@keyframes run {
    0% { transform: translateX(0) scaleX(1); }
    33% { transform: translateX(1px) scaleX(1.05); }
    66% { transform: translateX(-1px) scaleX(0.95); }
    100% { transform: translateX(0) scaleX(1); }
}

/* Mario Direction */
.mario.facing-left {
    transform: scaleX(-1);
}

.mario.facing-right {
    transform: scaleX(1);
}

/* Mario Power-up Transformation */
.mario.transforming {
    animation: transform 1s ease-in-out;
}

@keyframes transform {
    0%, 100% { opacity: 1; }
    25%, 75% { opacity: 0.5; transform: scale(1.1); }
    50% { opacity: 0.8; transform: scale(1.2); }
}

/* Mario Invincibility */
.mario.invincible {
    animation: invincible 0.2s steps(2) infinite;
}

@keyframes invincible {
    0% { opacity: 1; }
    50% { opacity: 0.3; }
    100% { opacity: 1; }
}

/* Mario Death Animation */
.mario.dying {
    animation: death 2s ease-out forwards;
}

@keyframes death {
    0% { transform: translateY(0) rotate(0deg); opacity: 1; }
    50% { transform: translateY(-50px) rotate(180deg); opacity: 0.8; }
    100% { transform: translateY(200px) rotate(360deg); opacity: 0; }
}

/* Mario Fireball */
.fireball {
    position: absolute;
    width: 8px;
    height: 8px;
    background: radial-gradient(circle, #FF4500 0%, #FF6347 50%, #FF8C00 100%);
    border-radius: 50%;
    box-shadow: 0 0 10px #FF4500;
    animation: fireball 0.1s ease-in-out infinite alternate;
}

@keyframes fireball {
    from { transform: scale(1); }
    to { transform: scale(1.2); }
}

/* Mario Victory Animation */
.mario.victory {
    animation: victory 2s ease-in-out infinite;
}

@keyframes victory {
    0%, 100% { transform: translateY(0); }
    25% { transform: translateY(-10px) rotate(10deg); }
    50% { transform: translateY(-5px) rotate(-10deg); }
    75% { transform: translateY(-15px) rotate(5deg); }
}
