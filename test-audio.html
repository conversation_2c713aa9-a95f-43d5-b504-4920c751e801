<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الأصوات - سوبر ماريو</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #87CEEB 0%, #98FB98 100%);
            margin: 0;
            padding: 20px;
            direction: rtl;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section h2 {
            color: #555;
            border-bottom: 2px solid #FFD700;
            padding-bottom: 10px;
        }
        
        .button-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        button {
            padding: 12px 20px;
            font-size: 14px;
            background: linear-gradient(45deg, #4169E1, #6495ED);
            color: white;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
            background: linear-gradient(45deg, #6495ED, #4169E1);
        }
        
        button:active {
            transform: translateY(0);
        }
        
        .music-button {
            background: linear-gradient(45deg, #FF6B6B, #FF8E53);
        }
        
        .music-button:hover {
            background: linear-gradient(45deg, #FF8E53, #FF6B6B);
        }
        
        .control-section {
            text-align: center;
            margin-bottom: 20px;
        }
        
        .volume-control {
            margin: 10px;
        }
        
        .volume-control label {
            display: inline-block;
            width: 100px;
            text-align: right;
        }
        
        input[type="range"] {
            width: 200px;
            margin: 0 10px;
        }
        
        .status {
            background: #f0f0f0;
            padding: 10px;
            border-radius: 5px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎵 اختبار نظام الأصوات - سوبر ماريو</h1>
        
        <div class="control-section">
            <div class="volume-control">
                <label>مستوى الأصوات:</label>
                <input type="range" id="soundVolume" min="0" max="1" step="0.1" value="0.5">
                <span id="soundVolumeValue">50%</span>
            </div>
            
            <div class="volume-control">
                <label>مستوى الموسيقى:</label>
                <input type="range" id="musicVolume" min="0" max="1" step="0.1" value="0.3">
                <span id="musicVolumeValue">30%</span>
            </div>
            
            <button onclick="toggleMute()">كتم/إلغاء كتم الأصوات</button>
            <button onclick="toggleMusicMute()">كتم/إلغاء كتم الموسيقى</button>
        </div>
        
        <div class="section">
            <h2>🎮 أصوات التأثيرات</h2>
            <div class="button-grid">
                <button onclick="playTestSound('jump')">القفز</button>
                <button onclick="playTestSound('coin')">العملة</button>
                <button onclick="playTestSound('powerup')">القوة الخاصة</button>
                <button onclick="playTestSound('fireball')">كرة النار</button>
                <button onclick="playTestSound('stomp')">دعس العدو</button>
                <button onclick="playTestSound('break')">كسر المكعب</button>
                <button onclick="playTestSound('death')">الموت</button>
                <button onclick="playTestSound('gameover')">انتهاء اللعبة</button>
                <button onclick="playTestSound('victory')">الفوز</button>
                <button onclick="playTestSound('flagpole')">لمس العلم</button>
                <button onclick="playTestSound('1up')">حياة إضافية</button>
                <button onclick="playTestSound('star')">قوة النجمة</button>
                <button onclick="playTestSound('pipe')">دخول الأنبوب</button>
                <button onclick="playTestSound('kick')">ركل الصدفة</button>
                <button onclick="playTestSound('bump')">ضرب المكعب</button>
            </div>
        </div>
        
        <div class="section">
            <h2>🎵 الموسيقى</h2>
            <div class="button-grid">
                <button class="music-button" onclick="playTestMusic('overworld')">الأراضي الخضراء</button>
                <button class="music-button" onclick="playTestMusic('underground')">تحت الأرض</button>
                <button class="music-button" onclick="playTestMusic('underwater')">تحت الماء</button>
                <button class="music-button" onclick="playTestMusic('castle')">القلعة</button>
                <button class="music-button" onclick="playTestMusic('sky')">السماء</button>
                <button class="music-button" onclick="playTestMusic('invincible')">قوة النجمة</button>
                <button class="music-button" onclick="stopMusic()">إيقاف الموسيقى</button>
            </div>
        </div>
        
        <div class="status" id="status">
            جاهز للاختبار...
        </div>
    </div>

    <script src="js/audio.js"></script>
    <script>
        // Initialize audio manager
        let audioManager;
        
        document.addEventListener('DOMContentLoaded', () => {
            audioManager = new AudioManager();
            updateStatus('تم تهيئة نظام الأصوات');
            
            // Setup volume controls
            setupVolumeControls();
        });
        
        function setupVolumeControls() {
            const soundVolumeSlider = document.getElementById('soundVolume');
            const musicVolumeSlider = document.getElementById('musicVolume');
            const soundVolumeValue = document.getElementById('soundVolumeValue');
            const musicVolumeValue = document.getElementById('musicVolumeValue');
            
            soundVolumeSlider.addEventListener('input', (e) => {
                const value = parseFloat(e.target.value);
                audioManager.setSoundVolume(value);
                soundVolumeValue.textContent = Math.round(value * 100) + '%';
                updateStatus(`تم تغيير مستوى الأصوات إلى ${Math.round(value * 100)}%`);
            });
            
            musicVolumeSlider.addEventListener('input', (e) => {
                const value = parseFloat(e.target.value);
                audioManager.setMusicVolume(value);
                musicVolumeValue.textContent = Math.round(value * 100) + '%';
                updateStatus(`تم تغيير مستوى الموسيقى إلى ${Math.round(value * 100)}%`);
            });
        }
        
        function playTestSound(soundName) {
            if (audioManager) {
                audioManager.playSound(soundName);
                updateStatus(`تم تشغيل صوت: ${soundName}`);
            }
        }
        
        function playTestMusic(musicName) {
            if (audioManager) {
                audioManager.playMusic(musicName);
                updateStatus(`تم تشغيل موسيقى: ${musicName}`);
            }
        }
        
        function stopMusic() {
            if (audioManager) {
                audioManager.stopMusic();
                updateStatus('تم إيقاف الموسيقى');
            }
        }
        
        function toggleMute() {
            if (audioManager) {
                audioManager.toggleMute();
                updateStatus(`تم ${audioManager.muted ? 'كتم' : 'إلغاء كتم'} الأصوات`);
            }
        }
        
        function toggleMusicMute() {
            if (audioManager) {
                audioManager.toggleMusicMute();
                updateStatus(`تم ${audioManager.musicMuted ? 'كتم' : 'إلغاء كتم'} الموسيقى`);
            }
        }
        
        function updateStatus(message) {
            const status = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            status.textContent = `[${timestamp}] ${message}`;
        }
        
        // Handle audio context activation (required by browsers)
        document.addEventListener('click', () => {
            if (audioManager && audioManager.audioContext && audioManager.audioContext.state === 'suspended') {
                audioManager.audioContext.resume();
                updateStatus('تم تفعيل نظام الصوت');
            }
        }, { once: true });
    </script>
</body>
</html>
