// Enemy Classes for Super Mario Game

// Base Enemy Class
class Enemy {
    constructor(x, y, width, height) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
        this.velocityX = -1;
        this.velocityY = 0;
        this.onGround = false;
        this.alive = true;
        this.direction = -1; // -1 for left, 1 for right
        this.animationFrame = 0;
        this.animationTimer = 0;
        this.type = 'enemy';
    }
    
    update(deltaTime, physics, platforms, walls) {
        if (!this.alive) return;
        
        this.updateAnimation(deltaTime);
        this.updateMovement();
        physics.update(this, platforms, walls);
        this.checkBoundaries();
    }
    
    updateAnimation(deltaTime) {
        this.animationTimer += deltaTime;
        if (this.animationTimer >= 500) {
            this.animationFrame = (this.animationFrame + 1) % 2;
            this.animationTimer = 0;
        }
    }
    
    updateMovement() {
        this.velocityX = this.direction * Math.abs(this.velocityX);
    }
    
    checkBoundaries() {
        if (this.x < -50 || this.x > 1250) {
            this.alive = false;
        }
    }
    
    changeDirection() {
        this.direction *= -1;
    }
    
    takeDamage() {
        this.alive = false;
        return 100; // Score points
    }
    
    getBounds() {
        return {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height
        };
    }
}

// Goomba Enemy
class Goomba extends Enemy {
    constructor(x, y) {
        super(x, y, 32, 32);
        this.velocityX = -1;
        this.type = 'goomba';
        this.squashed = false;
    }
    
    takeDamage(damageType = 'stomp') {
        if (damageType === 'stomp') {
            this.squashed = true;
            this.height = 8;
            setTimeout(() => {
                this.alive = false;
            }, 500);
            return 100;
        } else {
            this.alive = false;
            return 100;
        }
    }
    
    getSpriteClass() {
        let classes = ['goomba'];
        if (this.squashed) classes.push('squashed');
        return classes.join(' ');
    }
}

// Koopa Troopa Enemy
class Koopa extends Enemy {
    constructor(x, y, color = 'green') {
        super(x, y, 32, 48);
        this.velocityX = -1;
        this.type = 'koopa';
        this.color = color;
        this.inShell = false;
        this.shellMoving = false;
        this.shellSpeed = 8;
    }
    
    takeDamage(damageType = 'stomp') {
        if (damageType === 'stomp' && !this.inShell) {
            this.inShell = true;
            this.height = 24;
            this.velocityX = 0;
            return 100;
        } else if (damageType === 'stomp' && this.inShell && !this.shellMoving) {
            this.shellMoving = true;
            this.velocityX = this.direction * this.shellSpeed;
            return 200;
        } else if (damageType === 'kick' && this.inShell) {
            this.shellMoving = true;
            this.velocityX = this.direction * this.shellSpeed;
            return 200;
        } else {
            this.alive = false;
            return 100;
        }
    }
    
    updateMovement() {
        if (this.inShell && this.shellMoving) {
            // Shell maintains its velocity
        } else if (this.inShell) {
            this.velocityX = 0;
        } else {
            super.updateMovement();
        }
    }
    
    getSpriteClass() {
        let classes = ['koopa', this.color];
        if (this.inShell) classes.push('shell-only');
        return classes.join(' ');
    }
}

// Piranha Plant Enemy
class PiranhaPlant extends Enemy {
    constructor(x, y) {
        super(x, y, 32, 48);
        this.velocityX = 0;
        this.type = 'piranha';
        this.emerging = false;
        this.emergeTimer = 0;
        this.emergeInterval = 3000; // 3 seconds
        this.visible = false;
        this.pipeY = y + 48; // Bottom of pipe
    }
    
    update(deltaTime, physics, platforms, walls) {
        this.emergeTimer += deltaTime;
        
        if (this.emergeTimer >= this.emergeInterval) {
            this.emerging = true;
            this.visible = true;
            this.emergeTimer = 0;
        }
        
        if (this.emerging) {
            // Animate emergence
            if (this.y > this.pipeY - 48) {
                this.y -= 2;
            } else {
                setTimeout(() => {
                    this.emerging = false;
                    this.visible = false;
                    this.y = this.pipeY;
                }, 2000);
            }
        }
    }
    
    takeDamage(damageType = 'fireball') {
        if (damageType === 'fireball') {
            this.alive = false;
            return 200;
        }
        return 0; // Can't be stomped
    }
    
    getSpriteClass() {
        let classes = ['piranha'];
        if (this.emerging) classes.push('emerging');
        return classes.join(' ');
    }
}

// Bullet Bill Enemy
class BulletBill extends Enemy {
    constructor(x, y, direction = -1) {
        super(x, y, 32, 16);
        this.velocityX = direction * 4;
        this.direction = direction;
        this.type = 'bullet';
        this.velocityY = 0; // No gravity
    }
    
    update(deltaTime, physics, platforms, walls) {
        // Bullet Bills ignore gravity and platforms
        this.x += this.velocityX;
        this.checkBoundaries();
    }
    
    takeDamage(damageType = 'fireball') {
        if (damageType === 'fireball') {
            this.alive = false;
            return 200;
        }
        return 0; // Can't be stomped
    }
    
    getSpriteClass() {
        return 'bullet';
    }
}

// Lakitu Enemy
class Lakitu extends Enemy {
    constructor(x, y) {
        super(x, y, 48, 48);
        this.velocityX = -0.5;
        this.type = 'lakitu';
        this.throwTimer = 0;
        this.throwInterval = 4000; // 4 seconds
        this.spinies = [];
        this.floatOffset = 0;
    }
    
    update(deltaTime, physics, platforms, walls) {
        this.updateAnimation(deltaTime);
        this.updateFloating(deltaTime);
        this.updateThrowing(deltaTime);
        this.updateSpinies(deltaTime, physics, platforms, walls);
        
        // Move horizontally
        this.x += this.velocityX;
        this.checkBoundaries();
    }
    
    updateFloating(deltaTime) {
        this.floatOffset += deltaTime * 0.002;
        this.y += Math.sin(this.floatOffset) * 0.5;
    }
    
    updateThrowing(deltaTime) {
        this.throwTimer += deltaTime;
        if (this.throwTimer >= this.throwInterval) {
            this.throwSpiny();
            this.throwTimer = 0;
        }
    }
    
    throwSpiny() {
        const spiny = new Spiny(this.x, this.y + 20);
        this.spinies.push(spiny);
    }
    
    updateSpinies(deltaTime, physics, platforms, walls) {
        for (let i = this.spinies.length - 1; i >= 0; i--) {
            const spiny = this.spinies[i];
            spiny.update(deltaTime, physics, platforms, walls);
            
            if (!spiny.alive) {
                this.spinies.splice(i, 1);
            }
        }
    }
    
    takeDamage(damageType = 'fireball') {
        if (damageType === 'fireball') {
            this.alive = false;
            return 800;
        }
        return 0; // Can't be stomped
    }
    
    getSpriteClass() {
        return 'lakitu';
    }
}

// Spiny Enemy
class Spiny extends Enemy {
    constructor(x, y) {
        super(x, y, 32, 24);
        this.velocityX = -1;
        this.type = 'spiny';
        this.spinning = true;
    }
    
    takeDamage(damageType = 'fireball') {
        if (damageType === 'fireball') {
            this.alive = false;
            return 200;
        }
        return 0; // Can't be stomped due to spikes
    }
    
    getSpriteClass() {
        return 'spiny';
    }
}

// Hammer Bro Enemy
class HammerBro extends Enemy {
    constructor(x, y) {
        super(x, y, 32, 48);
        this.velocityX = 0;
        this.type = 'hammerbro';
        this.throwTimer = 0;
        this.throwInterval = 2000; // 2 seconds
        this.hammers = [];
        this.jumpTimer = 0;
        this.jumpInterval = 3000; // 3 seconds
    }
    
    update(deltaTime, physics, platforms, walls) {
        this.updateAnimation(deltaTime);
        this.updateThrowing(deltaTime);
        this.updateJumping(deltaTime, physics);
        this.updateHammers(deltaTime, physics, platforms, walls);
        
        physics.update(this, platforms, walls);
        this.checkBoundaries();
    }
    
    updateThrowing(deltaTime) {
        this.throwTimer += deltaTime;
        if (this.throwTimer >= this.throwInterval) {
            this.throwHammer();
            this.throwTimer = 0;
        }
    }
    
    updateJumping(deltaTime, physics) {
        this.jumpTimer += deltaTime;
        if (this.jumpTimer >= this.jumpInterval && this.onGround) {
            physics.jump(this, -10);
            this.jumpTimer = 0;
        }
    }
    
    throwHammer() {
        const hammer = new Hammer(this.x, this.y, -3, -8);
        this.hammers.push(hammer);
    }
    
    updateHammers(deltaTime, physics, platforms, walls) {
        for (let i = this.hammers.length - 1; i >= 0; i--) {
            const hammer = this.hammers[i];
            hammer.update(deltaTime, physics, platforms, walls);
            
            if (hammer.x < -50 || hammer.x > 1250 || hammer.y > 650) {
                this.hammers.splice(i, 1);
            }
        }
    }
    
    takeDamage(damageType = 'stomp') {
        this.alive = false;
        return 1000;
    }
    
    getSpriteClass() {
        return 'hammerbro';
    }
}

// Hammer Projectile
class Hammer {
    constructor(x, y, velocityX, velocityY) {
        this.x = x;
        this.y = y;
        this.width = 16;
        this.height = 16;
        this.velocityX = velocityX;
        this.velocityY = velocityY;
        this.rotation = 0;
        this.onGround = false;
    }
    
    update(deltaTime, physics, platforms, walls) {
        physics.applyGravity(this);
        physics.updatePosition(this);
        this.rotation += 10; // Spinning effect
    }
    
    getBounds() {
        return {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height
        };
    }
}

// Export classes
window.Enemy = Enemy;
window.Goomba = Goomba;
window.Koopa = Koopa;
window.PiranhaPlant = PiranhaPlant;
window.BulletBill = BulletBill;
window.Lakitu = Lakitu;
window.Spiny = Spiny;
window.HammerBro = HammerBro;
window.Hammer = Hammer;
