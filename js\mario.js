// Mario Character Class

class Mario {
    constructor(x, y) {
        // Position and dimensions
        this.x = x;
        this.y = y;
        this.width = 32;
        this.height = 32;
        
        // Physics properties
        this.velocityX = 0;
        this.velocityY = 0;
        this.onGround = false;
        this.facing = 'right';
        
        // State properties
        this.state = 'small'; // small, super, fire
        this.lives = 3;
        this.invincible = false;
        this.invincibilityTime = 0;
        this.powerUpTime = 0;
        
        // Animation properties
        this.animationFrame = 0;
        this.animationTimer = 0;
        this.currentAnimation = 'idle';
        
        // Input properties
        this.keys = {
            left: false,
            right: false,
            jump: false,
            run: false,
            fire: false
        };
        
        // Abilities
        this.canDoubleJump = false;
        this.hasDoubleJumped = false;
        this.canWallJump = false;
        this.canFly = false;
        this.flyTime = 0;
        
        // Fireballs
        this.fireballs = [];
        this.fireballCooldown = 0;
        
        // Sound effects
        this.sounds = {
            jump: new Audio('sounds/jump.wav'),
            powerup: new Audio('sounds/powerup.wav'),
            fireball: new Audio('sounds/fireball.wav'),
            death: new Audio('sounds/death.wav')
        };
        
        // Initialize sound volumes
        Object.values(this.sounds).forEach(sound => {
            sound.volume = 0.3;
        });
    }
    
    // Update Mario's state
    update(deltaTime, physics, platforms, walls, ceilings) {
        this.handleInput();
        this.updateTimers(deltaTime);
        this.updateAnimation(deltaTime);
        this.updateFireballs(deltaTime, physics, platforms, walls);
        
        // Apply physics
        physics.update(this, platforms, walls, ceilings);
        
        // Reset double jump when on ground
        if (this.onGround) {
            this.hasDoubleJumped = false;
        }
        
        // Update size based on state
        this.updateSize();
        
        // Check boundaries
        this.checkBoundaries();
    }
    
    // Handle player input
    handleInput() {
        // Horizontal movement
        if (this.keys.left) {
            this.facing = 'left';
            this.moveLeft();
        } else if (this.keys.right) {
            this.facing = 'right';
            this.moveRight();
        }
        
        // Jumping
        if (this.keys.jump) {
            this.jump();
        }
        
        // Fireball
        if (this.keys.fire && this.state === 'fire') {
            this.shootFireball();
        }
    }
    
    // Move left
    moveLeft() {
        const speed = this.keys.run ? 6 : 4;
        this.velocityX = Math.max(this.velocityX - 0.5, -speed);
        this.currentAnimation = this.keys.run ? 'running' : 'walking';
    }
    
    // Move right
    moveRight() {
        const speed = this.keys.run ? 6 : 4;
        this.velocityX = Math.min(this.velocityX + 0.5, speed);
        this.currentAnimation = this.keys.run ? 'running' : 'walking';
    }
    
    // Jump
    jump() {
        if (this.onGround) {
            this.velocityY = -15;
            this.onGround = false;
            this.currentAnimation = 'jumping';
            this.playSound('jump');
        } else if (this.canDoubleJump && !this.hasDoubleJumped) {
            this.velocityY = -12;
            this.hasDoubleJumped = true;
            this.currentAnimation = 'jumping';
            this.playSound('jump');
        }
    }
    
    // Shoot fireball
    shootFireball() {
        if (this.fireballCooldown <= 0 && this.fireballs.length < 2) {
            const fireball = new Fireball(
                this.x + (this.facing === 'right' ? this.width : 0),
                this.y + this.height / 2,
                this.facing === 'right' ? 8 : -8
            );
            this.fireballs.push(fireball);
            this.fireballCooldown = 300; // 300ms cooldown
            this.playSound('fireball');
        }
    }
    
    // Update timers
    updateTimers(deltaTime) {
        if (this.invincibilityTime > 0) {
            this.invincibilityTime -= deltaTime;
            if (this.invincibilityTime <= 0) {
                this.invincible = false;
            }
        }
        
        if (this.powerUpTime > 0) {
            this.powerUpTime -= deltaTime;
        }
        
        if (this.fireballCooldown > 0) {
            this.fireballCooldown -= deltaTime;
        }
        
        if (this.flyTime > 0) {
            this.flyTime -= deltaTime;
        }
    }
    
    // Update animation
    updateAnimation(deltaTime) {
        this.animationTimer += deltaTime;
        
        if (this.animationTimer >= 150) { // Change frame every 150ms
            this.animationFrame = (this.animationFrame + 1) % 4;
            this.animationTimer = 0;
        }
        
        // Set animation based on state
        if (!this.onGround) {
            this.currentAnimation = 'jumping';
        } else if (Math.abs(this.velocityX) < 0.1) {
            this.currentAnimation = 'idle';
        }
    }
    
    // Update fireballs
    updateFireballs(deltaTime, physics, platforms, walls) {
        for (let i = this.fireballs.length - 1; i >= 0; i--) {
            const fireball = this.fireballs[i];
            fireball.update(deltaTime, physics, platforms, walls);
            
            // Remove fireball if it's off screen or expired
            if (fireball.x < -50 || fireball.x > 1250 || fireball.expired) {
                this.fireballs.splice(i, 1);
            }
        }
    }
    
    // Update size based on power state
    updateSize() {
        switch (this.state) {
            case 'small':
                this.width = 32;
                this.height = 32;
                break;
            case 'super':
            case 'fire':
                this.width = 32;
                this.height = 48;
                break;
        }
    }
    
    // Check screen boundaries
    checkBoundaries() {
        // Left boundary
        if (this.x < 0) {
            this.x = 0;
            this.velocityX = 0;
        }
        
        // Right boundary (will be handled by camera later)
        if (this.x > 1200 - this.width) {
            this.x = 1200 - this.width;
            this.velocityX = 0;
        }
        
        // Death boundary (fall off screen)
        if (this.y > 650) {
            this.die();
        }
    }
    
    // Power up Mario
    powerUp(type) {
        switch (type) {
            case 'super':
                if (this.state === 'small') {
                    this.state = 'super';
                    this.playSound('powerup');
                    this.startTransformation();
                }
                break;
            case 'fire':
                this.state = 'fire';
                this.playSound('powerup');
                this.startTransformation();
                break;
            case 'star':
                this.invincible = true;
                this.invincibilityTime = 10000; // 10 seconds
                this.playSound('powerup');
                break;
            case '1up':
                this.lives++;
                this.playSound('powerup');
                break;
        }
    }
    
    // Start transformation animation
    startTransformation() {
        this.powerUpTime = 1000; // 1 second transformation
    }
    
    // Take damage
    takeDamage() {
        if (this.invincible) return false;
        
        if (this.state === 'fire' || this.state === 'super') {
            this.state = 'small';
            this.invincible = true;
            this.invincibilityTime = 2000; // 2 seconds invincibility
            return false; // Didn't die
        } else {
            this.die();
            return true; // Died
        }
    }
    
    // Die
    die() {
        this.lives--;
        this.playSound('death');
        this.currentAnimation = 'dying';
        
        if (this.lives <= 0) {
            // Game over
            return 'gameOver';
        } else {
            // Respawn
            return 'respawn';
        }
    }
    
    // Reset position
    reset(x, y) {
        this.x = x;
        this.y = y;
        this.velocityX = 0;
        this.velocityY = 0;
        this.onGround = false;
        this.state = 'small';
        this.invincible = false;
        this.invincibilityTime = 0;
        this.currentAnimation = 'idle';
        this.fireballs = [];
    }
    
    // Play sound effect
    playSound(soundName) {
        if (this.sounds[soundName]) {
            this.sounds[soundName].currentTime = 0;
            this.sounds[soundName].play().catch(e => {
                // Handle audio play errors silently
            });
        }
    }
    
    // Get current sprite class for CSS
    getSpriteClass() {
        let classes = ['mario', this.state];
        
        if (this.facing === 'left') {
            classes.push('facing-left');
        }
        
        if (this.currentAnimation === 'walking' || this.currentAnimation === 'running') {
            classes.push('walking');
        }
        
        if (this.currentAnimation === 'jumping') {
            classes.push('jumping');
        }
        
        if (this.currentAnimation === 'running') {
            classes.push('running');
        }
        
        if (this.invincible) {
            classes.push('invincible');
        }
        
        if (this.powerUpTime > 0) {
            classes.push('transforming');
        }
        
        if (this.currentAnimation === 'dying') {
            classes.push('dying');
        }
        
        return classes.join(' ');
    }
    
    // Get bounding box for collision detection
    getBounds() {
        return {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height
        };
    }
}

// Fireball class
class Fireball {
    constructor(x, y, velocityX) {
        this.x = x;
        this.y = y;
        this.width = 8;
        this.height = 8;
        this.velocityX = velocityX;
        this.velocityY = -2;
        this.bounces = 0;
        this.maxBounces = 3;
        this.expired = false;
        this.onGround = false;
    }
    
    update(deltaTime, physics, platforms, walls) {
        // Apply gravity
        physics.applyGravity(this);
        
        // Check ground collision
        physics.checkGroundCollision(this, platforms);
        
        // Bounce on ground
        if (this.onGround && this.velocityY > 0) {
            this.velocityY = -8;
            this.bounces++;
            
            if (this.bounces >= this.maxBounces) {
                this.expired = true;
            }
        }
        
        // Update position
        physics.updatePosition(this);
    }
    
    getBounds() {
        return {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height
        };
    }
}

// Export for use in other files
window.Mario = Mario;
window.Fireball = Fireball;
