// Power-up Classes for Super Mario Game

// Base Power-up Class
class PowerUp {
    constructor(x, y, width = 32, height = 32) {
        this.x = x;
        this.y = y;
        this.width = width;
        this.height = height;
        this.velocityX = 2;
        this.velocityY = 0;
        this.onGround = false;
        this.collected = false;
        this.type = 'powerup';
        this.animationFrame = 0;
        this.animationTimer = 0;
        this.direction = 1;
        this.spawning = true;
        this.spawnTimer = 0;
        this.spawnDuration = 500;
    }
    
    update(deltaTime, physics, platforms, walls) {
        if (this.collected) return;
        
        this.updateSpawning(deltaTime);
        this.updateAnimation(deltaTime);
        this.updateMovement();
        
        if (!this.spawning) {
            physics.update(this, platforms, walls);
        }
        
        this.checkBoundaries();
    }
    
    updateSpawning(deltaTime) {
        if (this.spawning) {
            this.spawnTimer += deltaTime;
            if (this.spawnTimer >= this.spawnDuration) {
                this.spawning = false;
            } else {
                // Move up during spawn
                this.y -= 1;
            }
        }
    }
    
    updateAnimation(deltaTime) {
        this.animationTimer += deltaTime;
        if (this.animationTimer >= 200) {
            this.animationFrame = (this.animationFrame + 1) % 4;
            this.animationTimer = 0;
        }
    }
    
    updateMovement() {
        if (!this.spawning) {
            this.velocityX = this.direction * 2;
        }
    }
    
    checkBoundaries() {
        if (this.x < -50 || this.x > 1250 || this.y > 650) {
            this.collected = true; // Remove from game
        }
    }
    
    checkCollision(mario) {
        if (this.collected || this.spawning) return false;
        
        return this.x < mario.x + mario.width &&
               this.x + this.width > mario.x &&
               this.y < mario.y + mario.height &&
               this.y + this.height > mario.y;
    }
    
    onCollect(mario) {
        this.collected = true;
        return { type: this.type, score: 1000 };
    }
    
    changeDirection() {
        this.direction *= -1;
    }
    
    getBounds() {
        return {
            x: this.x,
            y: this.y,
            width: this.width,
            height: this.height
        };
    }
    
    getSpriteClass() {
        let classes = [this.type];
        if (this.spawning) classes.push('spawning');
        if (this.collected) classes.push('collecting');
        return classes.join(' ');
    }
}

// Super Mushroom
class SuperMushroom extends PowerUp {
    constructor(x, y) {
        super(x, y);
        this.type = 'super-mushroom';
    }
    
    onCollect(mario) {
        this.collected = true;
        mario.powerUp('super');
        return { type: 'super', score: 1000 };
    }
}

// Fire Flower
class FireFlower extends PowerUp {
    constructor(x, y) {
        super(x, y);
        this.type = 'fire-flower';
        this.velocityX = 0; // Fire flowers don't move
    }
    
    updateMovement() {
        // Fire flowers stay in place
        this.velocityX = 0;
    }
    
    onCollect(mario) {
        this.collected = true;
        mario.powerUp('fire');
        return { type: 'fire', score: 1000 };
    }
}

// Star Power
class StarPower extends PowerUp {
    constructor(x, y) {
        super(x, y);
        this.type = 'star-power';
        this.bounceTimer = 0;
        this.bounceInterval = 300;
    }
    
    update(deltaTime, physics, platforms, walls) {
        super.update(deltaTime, physics, platforms, walls);
        this.updateBouncing(deltaTime, physics);
    }
    
    updateBouncing(deltaTime, physics) {
        this.bounceTimer += deltaTime;
        if (this.bounceTimer >= this.bounceInterval && this.onGround) {
            physics.jump(this, -12);
            this.bounceTimer = 0;
        }
    }
    
    onCollect(mario) {
        this.collected = true;
        mario.powerUp('star');
        return { type: 'star', score: 1000 };
    }
}

// 1-Up Mushroom
class OneUpMushroom extends PowerUp {
    constructor(x, y) {
        super(x, y);
        this.type = 'oneup-mushroom';
    }
    
    onCollect(mario) {
        this.collected = true;
        mario.powerUp('1up');
        return { type: '1up', score: 0 };
    }
}

// Cape Feather
class CapeFeather extends PowerUp {
    constructor(x, y) {
        super(x, y);
        this.type = 'cape-feather';
        this.floatTimer = 0;
    }
    
    update(deltaTime, physics, platforms, walls) {
        super.update(deltaTime, physics, platforms, walls);
        this.updateFloating(deltaTime);
    }
    
    updateFloating(deltaTime) {
        this.floatTimer += deltaTime;
        this.y += Math.sin(this.floatTimer * 0.005) * 0.5;
    }
    
    onCollect(mario) {
        this.collected = true;
        mario.powerUp('cape');
        mario.canFly = true;
        return { type: 'cape', score: 1000 };
    }
}

// Mini Mushroom
class MiniMushroom extends PowerUp {
    constructor(x, y) {
        super(x, y, 24, 24);
        this.type = 'mini-mushroom';
    }
    
    onCollect(mario) {
        this.collected = true;
        mario.powerUp('mini');
        return { type: 'mini', score: 1000 };
    }
}

// Mega Mushroom
class MegaMushroom extends PowerUp {
    constructor(x, y) {
        super(x, y, 48, 48);
        this.type = 'mega-mushroom';
        this.velocityX = 1; // Slower movement
    }
    
    onCollect(mario) {
        this.collected = true;
        mario.powerUp('mega');
        return { type: 'mega', score: 1000 };
    }
}

// Power-up Spawner
class PowerUpSpawner {
    constructor() {
        this.powerUps = [];
    }
    
    spawnPowerUp(type, x, y) {
        let powerUp;
        
        switch (type) {
            case 'super':
                powerUp = new SuperMushroom(x, y);
                break;
            case 'fire':
                powerUp = new FireFlower(x, y);
                break;
            case 'star':
                powerUp = new StarPower(x, y);
                break;
            case '1up':
                powerUp = new OneUpMushroom(x, y);
                break;
            case 'cape':
                powerUp = new CapeFeather(x, y);
                break;
            case 'mini':
                powerUp = new MiniMushroom(x, y);
                break;
            case 'mega':
                powerUp = new MegaMushroom(x, y);
                break;
            default:
                powerUp = new SuperMushroom(x, y);
        }
        
        this.powerUps.push(powerUp);
        return powerUp;
    }
    
    update(deltaTime, physics, platforms, walls, mario) {
        for (let i = this.powerUps.length - 1; i >= 0; i--) {
            const powerUp = this.powerUps[i];
            powerUp.update(deltaTime, physics, platforms, walls);
            
            // Check collision with Mario
            if (powerUp.checkCollision(mario)) {
                const result = powerUp.onCollect(mario);
                this.powerUps.splice(i, 1);
                return result;
            }
            
            // Remove collected or off-screen power-ups
            if (powerUp.collected) {
                this.powerUps.splice(i, 1);
            }
        }
        
        return null;
    }
    
    clear() {
        this.powerUps = [];
    }
    
    getPowerUps() {
        return this.powerUps;
    }
}

// Power-up Effects Manager
class PowerUpEffects {
    constructor() {
        this.activeEffects = [];
    }
    
    addEffect(type, duration, target) {
        const effect = {
            type: type,
            duration: duration,
            timeLeft: duration,
            target: target,
            active: true
        };
        
        this.activeEffects.push(effect);
        this.applyEffect(effect);
    }
    
    update(deltaTime) {
        for (let i = this.activeEffects.length - 1; i >= 0; i--) {
            const effect = this.activeEffects[i];
            effect.timeLeft -= deltaTime;
            
            if (effect.timeLeft <= 0) {
                this.removeEffect(effect);
                this.activeEffects.splice(i, 1);
            }
        }
    }
    
    applyEffect(effect) {
        switch (effect.type) {
            case 'invincibility':
                effect.target.invincible = true;
                break;
            case 'speed':
                effect.target.speedMultiplier = 2;
                break;
            case 'jump':
                effect.target.jumpMultiplier = 1.5;
                break;
            case 'magnet':
                effect.target.magneticCoins = true;
                break;
        }
    }
    
    removeEffect(effect) {
        switch (effect.type) {
            case 'invincibility':
                effect.target.invincible = false;
                break;
            case 'speed':
                effect.target.speedMultiplier = 1;
                break;
            case 'jump':
                effect.target.jumpMultiplier = 1;
                break;
            case 'magnet':
                effect.target.magneticCoins = false;
                break;
        }
    }
    
    hasEffect(type) {
        return this.activeEffects.some(effect => effect.type === type);
    }
    
    clear() {
        for (let effect of this.activeEffects) {
            this.removeEffect(effect);
        }
        this.activeEffects = [];
    }
}

// Power-up Detector (for smart power-up selection)
class PowerUpDetector {
    static getBestPowerUp(mario) {
        switch (mario.state) {
            case 'small':
                return 'super';
            case 'super':
                return Math.random() < 0.5 ? 'fire' : 'cape';
            case 'fire':
                return Math.random() < 0.3 ? 'star' : '1up';
            default:
                return 'super';
        }
    }
    
    static getRandomPowerUp() {
        const powerUps = ['super', 'fire', 'star', '1up', 'cape'];
        return powerUps[Math.floor(Math.random() * powerUps.length)];
    }
    
    static getRarePowerUp() {
        const rarePowerUps = ['star', '1up', 'mega', 'cape'];
        return rarePowerUps[Math.floor(Math.random() * rarePowerUps.length)];
    }
}

// Export classes
window.PowerUp = PowerUp;
window.SuperMushroom = SuperMushroom;
window.FireFlower = FireFlower;
window.StarPower = StarPower;
window.OneUpMushroom = OneUpMushroom;
window.CapeFeather = CapeFeather;
window.MiniMushroom = MiniMushroom;
window.MegaMushroom = MegaMushroom;
window.PowerUpSpawner = PowerUpSpawner;
window.PowerUpEffects = PowerUpEffects;
window.PowerUpDetector = PowerUpDetector;
