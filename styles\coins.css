/* Coin Styles */

/* Regular Coin */
.coin {
    position: absolute;
    width: 24px;
    height: 24px;
    z-index: 30;
}

.coin .coin-sprite {
    width: 24px;
    height: 24px;
    background: 
        radial-gradient(circle at 12px 12px, #FFD700 0%, #FFA500 60%, #FF8C00 100%);
    border: 2px solid #FF8C00;
    border-radius: 50%;
    position: relative;
    animation: coinSpin 2s linear infinite;
    box-shadow: 
        inset 2px 2px 4px rgba(255, 255, 255, 0.6),
        inset -2px -2px 4px rgba(0, 0, 0, 0.3),
        0 0 10px rgba(255, 215, 0, 0.5);
}

@keyframes coinSpin {
    0% { transform: rotateY(0deg); }
    25% { transform: rotateY(90deg) scaleX(0.3); }
    50% { transform: rotateY(180deg) scaleX(0.1); }
    75% { transform: rotateY(270deg) scaleX(0.3); }
    100% { transform: rotateY(360deg); }
}

.coin .coin-sprite::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    background: 
        radial-gradient(circle, #FFFF99 0%, #FFD700 100%);
    border-radius: 50%;
    box-shadow: inset 1px 1px 2px rgba(255, 255, 255, 0.8);
}

/* Coin Collection Effect */
.coin.collecting {
    animation: coinCollect 0.5s ease-out forwards;
}

@keyframes coinCollect {
    0% { 
        transform: scale(1) translateY(0);
        opacity: 1;
    }
    50% { 
        transform: scale(1.5) translateY(-20px);
        opacity: 0.8;
    }
    100% { 
        transform: scale(0) translateY(-40px);
        opacity: 0;
    }
}

/* Red Coin (Special) */
.coin.red {
    z-index: 35;
}

.coin.red .coin-sprite {
    background: 
        radial-gradient(circle at 12px 12px, #FF0000 0%, #CC0000 60%, #8B0000 100%);
    border-color: #8B0000;
    animation: redCoinSpin 1.5s linear infinite;
    box-shadow: 
        inset 2px 2px 4px rgba(255, 255, 255, 0.4),
        inset -2px -2px 4px rgba(0, 0, 0, 0.4),
        0 0 15px rgba(255, 0, 0, 0.7);
}

@keyframes redCoinSpin {
    0% { transform: rotateY(0deg); }
    25% { transform: rotateY(90deg) scaleX(0.3); }
    50% { transform: rotateY(180deg) scaleX(0.1); }
    75% { transform: rotateY(270deg) scaleX(0.3); }
    100% { transform: rotateY(360deg); }
}

.coin.red .coin-sprite::before {
    background: 
        radial-gradient(circle, #FF6666 0%, #FF0000 100%);
}

/* Blue Coin (Extra Special) */
.coin.blue {
    z-index: 35;
}

.coin.blue .coin-sprite {
    background: 
        radial-gradient(circle at 12px 12px, #0066FF 0%, #0044CC 60%, #002288 100%);
    border-color: #002288;
    animation: blueCoinSpin 1s linear infinite;
    box-shadow: 
        inset 2px 2px 4px rgba(255, 255, 255, 0.4),
        inset -2px -2px 4px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(0, 102, 255, 0.8);
}

@keyframes blueCoinSpin {
    0% { transform: rotateY(0deg) scale(1); }
    25% { transform: rotateY(90deg) scaleX(0.3) scale(1.1); }
    50% { transform: rotateY(180deg) scaleX(0.1) scale(1.2); }
    75% { transform: rotateY(270deg) scaleX(0.3) scale(1.1); }
    100% { transform: rotateY(360deg) scale(1); }
}

.coin.blue .coin-sprite::before {
    background: 
        radial-gradient(circle, #6699FF 0%, #0066FF 100%);
}

/* Coin Trail Effect */
.coin.trail {
    animation: coinTrail 0.3s ease-out forwards;
}

@keyframes coinTrail {
    0% { 
        transform: translateY(0);
        opacity: 1;
    }
    100% { 
        transform: translateY(-30px);
        opacity: 0;
    }
}

/* Coin Bounce Effect */
.coin.bouncing {
    animation: coinBounce 1s ease-in-out infinite;
}

@keyframes coinBounce {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-10px); }
}

/* Coin Magnet Effect */
.coin.magnetized {
    animation: coinMagnet 0.5s ease-in forwards;
}

@keyframes coinMagnet {
    0% { transform: scale(1); }
    50% { transform: scale(1.2); }
    100% { transform: scale(0.8); }
}

/* Coin Score Display */
.coin-score {
    position: absolute;
    font-size: 14px;
    font-weight: bold;
    color: #FFD700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    z-index: 100;
    pointer-events: none;
    animation: scoreFloat 1s ease-out forwards;
}

@keyframes scoreFloat {
    0% { 
        transform: translateY(0);
        opacity: 1;
    }
    100% { 
        transform: translateY(-50px);
        opacity: 0;
    }
}

/* Coin Ring Effect */
.coin-ring {
    position: absolute;
    width: 200px;
    height: 200px;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    z-index: 25;
}

.coin-ring .ring-coin {
    position: absolute;
    width: 16px;
    height: 16px;
    background: 
        radial-gradient(circle, #FFD700 0%, #FFA500 100%);
    border: 1px solid #FF8C00;
    border-radius: 50%;
    animation: ringRotate 3s linear infinite;
}

@keyframes ringRotate {
    from { transform: rotate(0deg) translateX(100px) rotate(0deg); }
    to { transform: rotate(360deg) translateX(100px) rotate(-360deg); }
}

.coin-ring .ring-coin:nth-child(1) { animation-delay: 0s; }
.coin-ring .ring-coin:nth-child(2) { animation-delay: 0.5s; }
.coin-ring .ring-coin:nth-child(3) { animation-delay: 1s; }
.coin-ring .ring-coin:nth-child(4) { animation-delay: 1.5s; }
.coin-ring .ring-coin:nth-child(5) { animation-delay: 2s; }
.coin-ring .ring-coin:nth-child(6) { animation-delay: 2.5s; }

/* Coin Fountain Effect */
.coin-fountain {
    position: absolute;
    width: 32px;
    height: 32px;
    z-index: 40;
}

.coin-fountain .fountain-coin {
    position: absolute;
    width: 12px;
    height: 12px;
    background: 
        radial-gradient(circle, #FFD700 0%, #FFA500 100%);
    border: 1px solid #FF8C00;
    border-radius: 50%;
    animation: fountainSpray 2s ease-out infinite;
}

@keyframes fountainSpray {
    0% { 
        transform: translateY(0) translateX(0);
        opacity: 1;
    }
    100% { 
        transform: translateY(-100px) translateX(var(--spray-x, 0px));
        opacity: 0;
    }
}

.coin-fountain .fountain-coin:nth-child(1) { --spray-x: -20px; animation-delay: 0s; }
.coin-fountain .fountain-coin:nth-child(2) { --spray-x: 20px; animation-delay: 0.2s; }
.coin-fountain .fountain-coin:nth-child(3) { --spray-x: -10px; animation-delay: 0.4s; }
.coin-fountain .fountain-coin:nth-child(4) { --spray-x: 10px; animation-delay: 0.6s; }
.coin-fountain .fountain-coin:nth-child(5) { --spray-x: 0px; animation-delay: 0.8s; }
