# ملفات الأصوات - لعبة سوبر ماريو

## 🎵 نظام الأصوات المدمج

**تم تطوير نظام أصوات اصطناعية متقدم!**

اللعبة الآن تحتوي على نظام توليد أصوات باستخدام **Web Audio API** يعمل بدون الحاجة لملفات صوتية خارجية.

### ✨ المميزات الجديدة:
- **أصوات اصطناعية عالية الجودة** لجميع التأثيرات
- **موسيقى خلفية مولدة** لكل مرحلة
- **تحكم كامل في مستوى الصوت**
- **عمل فوري** بدون تحميل ملفات

### 🧪 اختبار النظام:
افتح `test-audio.html` لاختبار جميع الأصوات والموسيقى.

---

## الأصوات المطلوبة (اختيارية):

إذا كنت تريد استخدام ملفات صوتية حقيقية بدلاً من الأصوات المولدة:

### أصوات التأثيرات:
- `jump.wav` - صوت القفز
- `coin.wav` - صوت جمع العملة
- `powerup.wav` - صوت الحصول على قوة خاصة
- `fireball.wav` - صوت إطلاق كرة النار
- `stomp.wav` - صوت دعس العدو
- `break.wav` - صوت كسر المكعب
- `death.wav` - صوت موت ماريو
- `gameover.wav` - صوت انتهاء اللعبة
- `victory.wav` - صوت الفوز
- `flagpole.wav` - صوت لمس العلم
- `1up.wav` - صوت الحصول على حياة إضافية
- `star.wav` - صوت قوة النجمة
- `pipe.wav` - صوت دخول الأنبوب
- `kick.wav` - صوت ركل الصدفة
- `bump.wav` - صوت ضرب المكعب

### الموسيقى:
- `overworld.mp3` - موسيقى المرحلة الأولى (الأراضي الخضراء)
- `underground.mp3` - موسيقى المرحلة الثانية (تحت الأرض)
- `underwater.mp3` - موسيقى المرحلة الثالثة (تحت الماء)
- `castle.mp3` - موسيقى المرحلة الرابعة (القلعة)
- `sky.mp3` - موسيقى المرحلة الخامسة (السماء)
- `boss.mp3` - موسيقى معركة الزعيم
- `invincible.mp3` - موسيقى قوة النجمة

## 🔧 كيفية العمل:

### الأصوات الاصطناعية:
- **القفز**: نغمة صاعدة من 400Hz إلى 600Hz
- **العملة**: نغمة مشرقة من 800Hz إلى 1200Hz
- **القوة الخاصة**: لحن صاعد (C-E-G-C)
- **كرة النار**: صوت منخفض متدرج
- **الموت**: نغمة حزينة متنازلة
- **الفوز**: لحن سعيد صاعد

### الموسيقى المولدة:
- **الأراضي الخضراء**: لحن سعيد ومرح
- **تحت الأرض**: نغمات منخفضة غامضة
- **تحت الماء**: أصوات متدفقة ناعمة
- **القلعة**: نغمات مظلمة ومخيفة
- **السماء**: أصوات خفيفة وهوائية
- **قوة النجمة**: لحن سريع ونشيط

## 📝 ملاحظات:
- النظام يعمل تلقائياً بدون ملفات خارجية
- يمكن إضافة ملفات صوتية حقيقية لاحقاً
- جميع الأصوات قابلة للتخصيص في `js/audio.js`
- يدعم التحكم في مستوى الصوت والكتم

## 🌐 مصادر الأصوات (اختيارية):
- [Freesound.org](https://freesound.org) - أصوات مجانية
- [Zapsplat](https://zapsplat.com) - مكتبة أصوات
- [BBC Sound Effects](https://sound-effects.bbcrewind.co.uk) - أصوات BBC
- [OpenGameArt](https://opengameart.org) - أصوات ألعاب مفتوحة المصدر
