/* Main Game Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #87CEEB 0%, #98FB98 100%);
    overflow: hidden;
    direction: rtl;
}

#gameContainer {
    position: relative;
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
}

#gameCanvas {
    border: 3px solid #8B4513;
    border-radius: 10px;
    background: linear-gradient(to bottom, #87CEEB 0%, #87CEEB 70%, #90EE90 70%, #90EE90 100%);
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.3);
}

/* Screen Overlays */
.screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    z-index: 1000;
    transition: opacity 0.3s ease;
}

.screen.hidden {
    display: none;
}

.screen h1 {
    font-size: 4rem;
    margin-bottom: 2rem;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.5);
    color: #FFD700;
    animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
    from { text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.5), 0 0 10px #FFD700; }
    to { text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.5), 0 0 20px #FFD700, 0 0 30px #FFD700; }
}

.screen p {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    text-align: center;
}

.screen button {
    padding: 15px 30px;
    font-size: 1.2rem;
    background: linear-gradient(45deg, #FF6B6B, #FF8E53);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.screen button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #FF8E53, #FF6B6B);
}

.controls {
    margin-top: 2rem;
    text-align: center;
}

.controls p {
    margin: 0.5rem 0;
    font-size: 1.1rem;
}

/* Game UI */
#gameUI {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 100;
    display: flex;
    gap: 20px;
    font-size: 1.2rem;
    font-weight: bold;
}

#gameUI > div {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 10px 15px;
    border-radius: 20px;
    border: 2px solid #FFD700;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

#gameUI span {
    color: #FFD700;
}

/* Responsive Design */
@media (max-width: 1300px) {
    #gameCanvas {
        width: 95vw;
        height: auto;
    }
}

@media (max-width: 768px) {
    .screen h1 {
        font-size: 2.5rem;
    }
    
    .screen p {
        font-size: 1.2rem;
    }
    
    #gameUI {
        flex-direction: column;
        gap: 10px;
        font-size: 1rem;
    }
    
    #gameUI > div {
        padding: 8px 12px;
    }
}

/* Loading Animation */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #FFD700;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}
