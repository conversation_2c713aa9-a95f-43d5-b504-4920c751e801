/* Block Styles */

/* Ground Block */
.ground-block {
    position: absolute;
    width: 32px;
    height: 32px;
    background: 
        linear-gradient(45deg, #8B4513 25%, transparent 25%),
        linear-gradient(-45deg, #8B4513 25%, transparent 25%),
        linear-gradient(45deg, transparent 75%, #8B4513 75%),
        linear-gradient(-45deg, transparent 75%, #8B4513 75%);
    background-size: 8px 8px;
    background-position: 0 0, 0 4px, 4px -4px, -4px 0px;
    background-color: #A0522D;
    border: 2px solid #654321;
    box-shadow: inset 2px 2px 4px rgba(255, 255, 255, 0.3);
}

/* Brick Block */
.brick-block {
    position: absolute;
    width: 32px;
    height: 32px;
    background: 
        repeating-linear-gradient(
            0deg,
            #CD853F 0px,
            #CD853F 8px,
            #8B4513 8px,
            #8B4513 10px
        ),
        repeating-linear-gradient(
            90deg,
            #CD853F 0px,
            #CD853F 16px,
            #8B4513 16px,
            #8B4513 18px
        );
    border: 1px solid #654321;
    box-shadow: 
        inset 2px 2px 4px rgba(255, 255, 255, 0.3),
        inset -2px -2px 4px rgba(0, 0, 0, 0.3);
    transition: all 0.1s ease;
}

.brick-block.breaking {
    animation: brickBreak 0.3s ease-out forwards;
}

@keyframes brickBreak {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); }
    100% { transform: scale(0); opacity: 0; }
}

/* Question Block */
.question-block {
    position: absolute;
    width: 32px;
    height: 32px;
    background: 
        linear-gradient(45deg, #FFD700 0%, #FFA500 100%);
    border: 2px solid #FF8C00;
    box-shadow: 
        inset 2px 2px 4px rgba(255, 255, 255, 0.5),
        inset -2px -2px 4px rgba(0, 0, 0, 0.2);
    position: relative;
    animation: questionGlow 2s ease-in-out infinite;
}

@keyframes questionGlow {
    0%, 100% { box-shadow: 
        inset 2px 2px 4px rgba(255, 255, 255, 0.5),
        inset -2px -2px 4px rgba(0, 0, 0, 0.2),
        0 0 5px #FFD700; }
    50% { box-shadow: 
        inset 2px 2px 4px rgba(255, 255, 255, 0.5),
        inset -2px -2px 4px rgba(0, 0, 0, 0.2),
        0 0 15px #FFD700, 0 0 25px #FFA500; }
}

.question-block::before {
    content: '?';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20px;
    font-weight: bold;
    color: #8B4513;
    text-shadow: 1px 1px 2px rgba(255, 255, 255, 0.8);
}

.question-block.used {
    background: #8B4513;
    animation: none;
    box-shadow: 
        inset 2px 2px 4px rgba(0, 0, 0, 0.3),
        inset -2px -2px 4px rgba(255, 255, 255, 0.1);
}

.question-block.used::before {
    content: '';
}

.question-block.hit {
    animation: blockHit 0.3s ease-out;
}

@keyframes blockHit {
    0%, 100% { transform: translateY(0); }
    50% { transform: translateY(-8px); }
}

/* Pipe */
.pipe {
    position: absolute;
    background: 
        linear-gradient(to right, #228B22 0%, #32CD32 50%, #228B22 100%);
    border: 2px solid #006400;
    border-radius: 8px 8px 0 0;
    box-shadow: 
        inset 4px 4px 8px rgba(255, 255, 255, 0.3),
        inset -4px -4px 8px rgba(0, 0, 0, 0.3);
}

.pipe.small {
    width: 64px;
    height: 64px;
}

.pipe.medium {
    width: 64px;
    height: 96px;
}

.pipe.large {
    width: 64px;
    height: 128px;
}

.pipe::before {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    height: 16px;
    background: 
        linear-gradient(to right, #228B22 0%, #32CD32 50%, #228B22 100%);
    border: 2px solid #006400;
    border-radius: 12px;
    box-shadow: 
        inset 4px 4px 8px rgba(255, 255, 255, 0.3),
        inset -4px -4px 8px rgba(0, 0, 0, 0.3);
}

/* Castle */
.castle {
    position: absolute;
    width: 160px;
    height: 128px;
    background: 
        linear-gradient(to bottom, #696969 0%, #808080 100%);
    border: 3px solid #2F4F4F;
}

.castle::before {
    content: '';
    position: absolute;
    top: -20px;
    left: 20px;
    right: 20px;
    height: 20px;
    background: 
        repeating-linear-gradient(
            90deg,
            #696969 0px,
            #696969 20px,
            transparent 20px,
            transparent 30px
        );
    border: 2px solid #2F4F4F;
}

.castle::after {
    content: '🏰';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 48px;
}

/* Flag Pole */
.flag-pole {
    position: absolute;
    width: 8px;
    height: 320px;
    background: 
        linear-gradient(to right, #8B4513 0%, #CD853F 50%, #8B4513 100%);
    border-radius: 4px;
}

.flag {
    position: absolute;
    top: 0;
    left: 8px;
    width: 32px;
    height: 24px;
    background: 
        linear-gradient(45deg, #FF0000 50%, #FFFFFF 50%);
    border: 1px solid #000;
    animation: flagWave 2s ease-in-out infinite;
}

@keyframes flagWave {
    0%, 100% { transform: rotate(0deg); }
    50% { transform: rotate(5deg); }
}

/* Moving Platform */
.moving-platform {
    position: absolute;
    width: 96px;
    height: 16px;
    background: 
        linear-gradient(to bottom, #8B4513 0%, #654321 100%);
    border: 2px solid #654321;
    border-radius: 8px;
    box-shadow: 
        inset 2px 2px 4px rgba(255, 255, 255, 0.3),
        0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Cloud Platform */
.cloud-platform {
    position: absolute;
    width: 96px;
    height: 32px;
    background: 
        radial-gradient(ellipse 48px 16px at 48px 16px, #FFFFFF 0%, #E0E0E0 70%, transparent 70%);
    border-radius: 50px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

/* Invisible Block */
.invisible-block {
    position: absolute;
    width: 32px;
    height: 32px;
    background: transparent;
    border: 1px dashed rgba(255, 255, 255, 0.3);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.invisible-block.revealed {
    opacity: 1;
    background: rgba(255, 255, 255, 0.5);
    animation: blockReveal 0.5s ease-out;
}

@keyframes blockReveal {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

/* Lava Block */
.lava-block {
    position: absolute;
    width: 32px;
    height: 32px;
    background: 
        radial-gradient(circle at 16px 16px, #FF4500 0%, #FF0000 50%, #8B0000 100%);
    border: 2px solid #8B0000;
    animation: lavaGlow 1s ease-in-out infinite alternate;
}

@keyframes lavaGlow {
    from { 
        box-shadow: 0 0 10px #FF4500;
        background: radial-gradient(circle at 16px 16px, #FF4500 0%, #FF0000 50%, #8B0000 100%);
    }
    to { 
        box-shadow: 0 0 20px #FF4500, 0 0 30px #FF0000;
        background: radial-gradient(circle at 16px 16px, #FF6347 0%, #FF4500 50%, #FF0000 100%);
    }
}
