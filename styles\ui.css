/* UI Styles */

/* Game HUD */
#gameUI {
    position: fixed;
    top: 20px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    z-index: 1000;
    font-family: 'Courier New', monospace;
}

.ui-panel {
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.9) 100%);
    border: 3px solid #FFD700;
    border-radius: 15px;
    padding: 10px 15px;
    color: #FFFFFF;
    font-weight: bold;
    font-size: 16px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    box-shadow: 
        0 4px 15px rgba(0, 0, 0, 0.3),
        inset 0 2px 5px rgba(255, 215, 0, 0.2);
    transition: all 0.3s ease;
}

.ui-panel:hover {
    transform: translateY(-2px);
    box-shadow: 
        0 6px 20px rgba(0, 0, 0, 0.4),
        inset 0 2px 5px rgba(255, 215, 0, 0.3);
}

.ui-value {
    color: #FFD700;
    font-size: 18px;
    margin-left: 10px;
}

/* Health Bar */
.health-bar {
    width: 150px;
    height: 20px;
    background: linear-gradient(to right, #333333 0%, #555555 100%);
    border: 2px solid #FFD700;
    border-radius: 10px;
    overflow: hidden;
    position: relative;
}

.health-fill {
    height: 100%;
    background: linear-gradient(to right, #FF0000 0%, #FF6B6B 50%, #FF0000 100%);
    transition: width 0.5s ease;
    border-radius: 8px;
    box-shadow: inset 0 2px 5px rgba(255, 255, 255, 0.3);
}

.health-fill.low {
    animation: healthPulse 1s ease-in-out infinite;
}

@keyframes healthPulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

/* Power Meter */
.power-meter {
    width: 120px;
    height: 15px;
    background: linear-gradient(to right, #333333 0%, #555555 100%);
    border: 2px solid #00BFFF;
    border-radius: 8px;
    overflow: hidden;
    position: relative;
}

.power-fill {
    height: 100%;
    background: linear-gradient(to right, #00BFFF 0%, #87CEEB 50%, #00BFFF 100%);
    transition: width 0.3s ease;
    border-radius: 6px;
    animation: powerGlow 2s ease-in-out infinite;
}

@keyframes powerGlow {
    0%, 100% { box-shadow: inset 0 2px 5px rgba(255, 255, 255, 0.3); }
    50% { box-shadow: inset 0 2px 5px rgba(255, 255, 255, 0.6), 0 0 10px #00BFFF; }
}

/* Timer */
.timer {
    font-size: 24px;
    color: #FFD700;
    text-align: center;
    animation: timerTick 1s ease-in-out infinite;
}

.timer.warning {
    color: #FF4500;
    animation: timerWarning 0.5s ease-in-out infinite;
}

.timer.critical {
    color: #FF0000;
    animation: timerCritical 0.3s ease-in-out infinite;
}

@keyframes timerTick {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes timerWarning {
    0%, 100% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.1); opacity: 0.8; }
}

@keyframes timerCritical {
    0%, 100% { transform: scale(1); color: #FF0000; }
    50% { transform: scale(1.2); color: #FFFFFF; }
}

/* Lives Display */
.lives-display {
    display: flex;
    align-items: center;
    gap: 10px;
}

.life-icon {
    width: 24px;
    height: 24px;
    background: 
        radial-gradient(circle at 12px 12px, #FF0000 0%, #CC0000 70%, transparent 70%);
    border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
    position: relative;
    animation: heartBeat 2s ease-in-out infinite;
}

@keyframes heartBeat {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Score Display */
.score-display {
    font-size: 20px;
    color: #FFD700;
    text-align: center;
    position: relative;
}

.score-popup {
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 16px;
    color: #00FF00;
    font-weight: bold;
    animation: scoreFloat 1s ease-out forwards;
    pointer-events: none;
}

@keyframes scoreFloat {
    0% { 
        transform: translateX(-50%) translateY(0);
        opacity: 1;
    }
    100% { 
        transform: translateX(-50%) translateY(-40px);
        opacity: 0;
    }
}

/* Level Progress */
.level-progress {
    width: 200px;
    height: 10px;
    background: linear-gradient(to right, #333333 0%, #555555 100%);
    border: 2px solid #FFD700;
    border-radius: 5px;
    overflow: hidden;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(to right, #32CD32 0%, #90EE90 50%, #32CD32 100%);
    transition: width 1s ease;
    border-radius: 3px;
    animation: progressGlow 3s ease-in-out infinite;
}

@keyframes progressGlow {
    0%, 100% { box-shadow: inset 0 1px 3px rgba(255, 255, 255, 0.3); }
    50% { box-shadow: inset 0 1px 3px rgba(255, 255, 255, 0.6), 0 0 8px #32CD32; }
}

/* Pause Menu */
.pause-menu {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.9) 0%, rgba(0, 0, 0, 0.95) 100%);
    border: 4px solid #FFD700;
    border-radius: 20px;
    padding: 30px;
    text-align: center;
    z-index: 2000;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
}

.pause-menu h2 {
    color: #FFD700;
    font-size: 2.5rem;
    margin-bottom: 20px;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8);
}

.pause-menu button {
    display: block;
    width: 200px;
    margin: 15px auto;
    padding: 15px 20px;
    font-size: 1.2rem;
    background: linear-gradient(45deg, #4169E1, #6495ED);
    color: white;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.pause-menu button:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    background: linear-gradient(45deg, #6495ED, #4169E1);
}

/* Game Over Overlay */
.game-over-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 0, 0, 0.8) 0%, rgba(0, 0, 0, 0.9) 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 3000;
    animation: gameOverFade 2s ease-in;
}

@keyframes gameOverFade {
    0% { opacity: 0; }
    100% { opacity: 1; }
}

.game-over-text {
    font-size: 4rem;
    color: #FFFFFF;
    text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.8);
    margin-bottom: 30px;
    animation: gameOverPulse 2s ease-in-out infinite;
}

@keyframes gameOverPulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

/* Victory Overlay */
.victory-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.8) 0%, rgba(0, 128, 0, 0.9) 100%);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 3000;
    animation: victoryFade 2s ease-in;
}

@keyframes victoryFade {
    0% { opacity: 0; transform: scale(0.5); }
    100% { opacity: 1; transform: scale(1); }
}

.victory-text {
    font-size: 4rem;
    color: #FFFFFF;
    text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.8);
    margin-bottom: 30px;
    animation: victoryGlow 2s ease-in-out infinite;
}

@keyframes victoryGlow {
    0%, 100% { 
        text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.8), 0 0 20px #FFD700;
    }
    50% { 
        text-shadow: 4px 4px 8px rgba(0, 0, 0, 0.8), 0 0 40px #FFD700, 0 0 60px #FFA500;
    }
}

/* Mobile Controls */
.mobile-controls {
    position: fixed;
    bottom: 20px;
    left: 20px;
    right: 20px;
    display: none;
    justify-content: space-between;
    z-index: 1000;
}

.control-pad {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.control-button {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, rgba(0, 0, 0, 0.7) 0%, rgba(0, 0, 0, 0.9) 100%);
    border: 3px solid #FFD700;
    border-radius: 50%;
    color: #FFFFFF;
    font-size: 24px;
    font-weight: bold;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 5px;
    touch-action: manipulation;
    user-select: none;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.control-button:active {
    transform: scale(0.95);
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.3) 0%, rgba(0, 0, 0, 0.9) 100%);
}

@media (max-width: 768px) {
    .mobile-controls {
        display: flex;
    }
    
    #gameUI {
        flex-direction: row;
        top: 10px;
        right: 10px;
        gap: 10px;
    }
    
    .ui-panel {
        padding: 8px 12px;
        font-size: 14px;
    }
}
