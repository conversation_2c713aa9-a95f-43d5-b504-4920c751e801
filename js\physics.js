// Physics Engine for Super Mario Game

class PhysicsEngine {
    constructor() {
        this.gravity = 0.8;
        this.friction = 0.85;
        this.airResistance = 0.98;
        this.terminalVelocity = 15;
        this.jumpPower = -15;
        this.walkSpeed = 4;
        this.runSpeed = 6;
    }

    // Apply gravity to an entity
    applyGravity(entity) {
        if (!entity.onGround) {
            entity.velocityY += this.gravity;
            if (entity.velocityY > this.terminalVelocity) {
                entity.velocityY = this.terminalVelocity;
            }
        }
    }

    // Apply friction to horizontal movement
    applyFriction(entity) {
        if (entity.onGround) {
            entity.velocityX *= this.friction;
        } else {
            entity.velocityX *= this.airResistance;
        }
    }

    // Update entity position based on velocity
    updatePosition(entity) {
        entity.x += entity.velocityX;
        entity.y += entity.velocityY;
    }

    // Check collision between two rectangles
    checkCollision(rect1, rect2) {
        return rect1.x < rect2.x + rect2.width &&
               rect1.x + rect1.width > rect2.x &&
               rect1.y < rect2.y + rect2.height &&
               rect1.y + rect1.height > rect2.y;
    }

    // Get collision side
    getCollisionSide(moving, stationary) {
        const overlapX = Math.min(moving.x + moving.width - stationary.x, 
                                 stationary.x + stationary.width - moving.x);
        const overlapY = Math.min(moving.y + moving.height - stationary.y, 
                                 stationary.y + stationary.height - moving.y);

        if (overlapX < overlapY) {
            // Horizontal collision
            if (moving.x < stationary.x) {
                return 'right'; // Moving object hit from the right
            } else {
                return 'left'; // Moving object hit from the left
            }
        } else {
            // Vertical collision
            if (moving.y < stationary.y) {
                return 'bottom'; // Moving object hit from below
            } else {
                return 'top'; // Moving object hit from above
            }
        }
    }

    // Resolve collision between two objects
    resolveCollision(moving, stationary, side) {
        switch (side) {
            case 'top':
                moving.y = stationary.y - moving.height;
                moving.velocityY = 0;
                moving.onGround = true;
                break;
            case 'bottom':
                moving.y = stationary.y + stationary.height;
                moving.velocityY = 0;
                break;
            case 'left':
                moving.x = stationary.x - moving.width;
                moving.velocityX = 0;
                break;
            case 'right':
                moving.x = stationary.x + stationary.width;
                moving.velocityX = 0;
                break;
        }
    }

    // Check if entity is on ground
    checkGroundCollision(entity, platforms) {
        entity.onGround = false;
        
        for (let platform of platforms) {
            if (this.checkCollision(entity, platform)) {
                const side = this.getCollisionSide(entity, platform);
                if (side === 'top' && entity.velocityY >= 0) {
                    this.resolveCollision(entity, platform, side);
                    return true;
                }
            }
        }
        return false;
    }

    // Check wall collisions
    checkWallCollisions(entity, walls) {
        for (let wall of walls) {
            if (this.checkCollision(entity, wall)) {
                const side = this.getCollisionSide(entity, wall);
                if (side === 'left' || side === 'right') {
                    this.resolveCollision(entity, wall, side);
                    return side;
                }
            }
        }
        return null;
    }

    // Check ceiling collisions
    checkCeilingCollisions(entity, ceilings) {
        for (let ceiling of ceilings) {
            if (this.checkCollision(entity, ceiling)) {
                const side = this.getCollisionSide(entity, ceiling);
                if (side === 'bottom' && entity.velocityY < 0) {
                    this.resolveCollision(entity, ceiling, side);
                    return true;
                }
            }
        }
        return false;
    }

    // Apply jump force
    jump(entity, power = null) {
        if (entity.onGround) {
            entity.velocityY = power || this.jumpPower;
            entity.onGround = false;
            return true;
        }
        return false;
    }

    // Move entity horizontally
    moveHorizontal(entity, direction, running = false) {
        const speed = running ? this.runSpeed : this.walkSpeed;
        entity.velocityX += direction * speed * 0.3;
        
        // Limit maximum speed
        const maxSpeed = running ? this.runSpeed : this.walkSpeed;
        if (entity.velocityX > maxSpeed) entity.velocityX = maxSpeed;
        if (entity.velocityX < -maxSpeed) entity.velocityX = -maxSpeed;
    }

    // Check if point is inside rectangle
    pointInRect(point, rect) {
        return point.x >= rect.x && 
               point.x <= rect.x + rect.width &&
               point.y >= rect.y && 
               point.y <= rect.y + rect.height;
    }

    // Get distance between two points
    getDistance(point1, point2) {
        const dx = point2.x - point1.x;
        const dy = point2.y - point1.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    // Check line intersection (for projectiles)
    lineIntersection(line1Start, line1End, line2Start, line2End) {
        const x1 = line1Start.x, y1 = line1Start.y;
        const x2 = line1End.x, y2 = line1End.y;
        const x3 = line2Start.x, y3 = line2Start.y;
        const x4 = line2End.x, y4 = line2End.y;

        const denom = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4);
        if (denom === 0) return null; // Lines are parallel

        const t = ((x1 - x3) * (y3 - y4) - (y1 - y3) * (x3 - x4)) / denom;
        const u = -((x1 - x2) * (y1 - y3) - (y1 - y2) * (x1 - x3)) / denom;

        if (t >= 0 && t <= 1 && u >= 0 && u <= 1) {
            return {
                x: x1 + t * (x2 - x1),
                y: y1 + t * (y2 - y1)
            };
        }
        return null;
    }

    // Apply bounce effect
    bounce(entity, surface, bounciness = 0.7) {
        const side = this.getCollisionSide(entity, surface);
        
        switch (side) {
            case 'top':
            case 'bottom':
                entity.velocityY *= -bounciness;
                break;
            case 'left':
            case 'right':
                entity.velocityX *= -bounciness;
                break;
        }
    }

    // Check if entity is moving towards another entity
    isMovingTowards(entity1, entity2) {
        const dx = entity2.x - entity1.x;
        const dy = entity2.y - entity1.y;
        
        return (dx > 0 && entity1.velocityX > 0) || 
               (dx < 0 && entity1.velocityX < 0) ||
               (dy > 0 && entity1.velocityY > 0) || 
               (dy < 0 && entity1.velocityY < 0);
    }

    // Apply force to entity
    applyForce(entity, forceX, forceY) {
        entity.velocityX += forceX;
        entity.velocityY += forceY;
    }

    // Clamp velocity to maximum values
    clampVelocity(entity, maxX = 10, maxY = 20) {
        entity.velocityX = Math.max(-maxX, Math.min(maxX, entity.velocityX));
        entity.velocityY = Math.max(-maxY, Math.min(maxY, entity.velocityY));
    }

    // Update physics for an entity
    update(entity, platforms = [], walls = [], ceilings = []) {
        // Apply physics forces
        this.applyGravity(entity);
        this.applyFriction(entity);
        
        // Check collisions
        this.checkGroundCollision(entity, platforms);
        this.checkWallCollisions(entity, walls);
        this.checkCeilingCollisions(entity, ceilings);
        
        // Clamp velocity
        this.clampVelocity(entity);
        
        // Update position
        this.updatePosition(entity);
    }
}

// Export for use in other files
window.PhysicsEngine = PhysicsEngine;
