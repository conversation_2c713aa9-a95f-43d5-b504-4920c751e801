# 🎮 لعبة سوبر ماريو العربية

لعبة سوبر ماريو احترافية مطورة بـ HTML5, CSS3, و JavaScript مع تصميم متقدم ومراحل ممتعة.

## 🌟 المميزات

### 🎨 تصميم متقدم
- **رسوميات CSS متطورة** مع animations و transitions
- **تصميم منفصل لكل عنصر** - كل شخصية ومجسم في ملف CSS منفصل
- **5 مراحل مختلفة** بتصاميم وخلفيات متنوعة
- **تأثيرات بصرية احترافية** مع إضاءة وظلال

### 🎮 طريقة اللعب
- **فيزياء واقعية** للقفز والحركة
- **أعداء متنوعة** مع ذكاء اصطناعي
- **قوى خاصة** (Super Mario, Fire Mario, Star Power)
- **نظام نقاط وحياة** متطور
- **عملات وجوائز** متنوعة

### 🎵 الصوت والموسيقى
- **نظام أصوات اصطناعية متقدم** باستخدام Web Audio API
- **15 تأثير صوتي مختلف** مولد بالكامل
- **6 مقطوعات موسيقية** لكل مرحلة ومناسبة
- **تحكم كامل في مستوى الصوت** والكتم
- **عمل فوري** بدون الحاجة لملفات خارجية

## 📁 هيكل المشروع

```
languege_arabic/
├── index.html              # الملف الرئيسي
├── test-audio.html         # اختبار نظام الأصوات
├── styles/                 # ملفات التنسيق
│   ├── main.css           # التنسيق العام
│   ├── mario.css          # تصميم شخصية ماريو
│   ├── enemies.css        # تصميم الأعداء
│   ├── blocks.css         # تصميم المكعبات
│   ├── coins.css          # تصميم العملات
│   ├── powerups.css       # تصميم القوى الخاصة
│   ├── background.css     # تصميم الخلفيات
│   └── ui.css             # واجهة المستخدم
├── js/                     # ملفات JavaScript
│   ├── physics.js         # محرك الفيزياء
│   ├── mario.js           # منطق شخصية ماريو
│   ├── enemies.js         # منطق الأعداء
│   ├── blocks.js          # منطق المكعبات
│   ├── coins.js           # منطق العملات
│   ├── powerups.js        # منطق القوى الخاصة
│   ├── levels.js          # تصميم المراحل
│   ├── audio.js           # إدارة الأصوات
│   └── game.js            # المحرك الرئيسي
├── sounds/                 # ملفات الأصوات
│   └── README.md          # دليل الأصوات
└── README.md              # هذا الملف
```

## 🎯 المراحل

### 🌱 المرحلة الأولى: مغامرة الأراضي الخضراء
- بيئة خضراء جميلة مع تلال وغيوم
- أعداء أساسيون (Goomba, Koopa)
- مكعبات أسئلة وعملات
- مدة: 400 ثانية

### 🕳️ المرحلة الثانية: كهوف تحت الأرض
- بيئة مظلمة مع كريستالات متوهجة
- ممرات ضيقة وعوائق صعبة
- نباتات Piranha في الأنابيب
- مدة: 350 ثانية

### 🌊 المرحلة الثالثة: مغامرة تحت الماء
- فيزياء مختلفة تحت الماء
- شعاب مرجانية وفقاعات
- أعداء سباحة
- مدة: 300 ثانية

### 🏰 المرحلة الرابعة: قلعة الحصن
- بيئة مظلمة مع برق ولافا
- أعداء أقوى (Hammer Bro, Lakitu)
- منصات متحركة فوق الحمم
- مدة: 250 ثانية

### ☁️ المرحلة الخامسة: مملكة السماء
- منصات سحابية عائمة
- قوس قزح وألوان زاهية
- تحدي نهائي صعب
- مدة: 400 ثانية

## 🎮 التحكم

- **الأسهم**: الحركة يميناً ويساراً
- **مسافة**: القفز
- **Shift**: الجري
- **X**: إطلاق كرة النار (Fire Mario)
- **Escape**: إيقاف/استكمال اللعبة

## 🚀 كيفية التشغيل

1. **افتح الملف**: `index.html` في متصفح حديث
2. **اضغط مسافة**: لبدء اللعبة
3. **استمتع**: بالمغامرة!

### 🎵 اختبار الأصوات:
- **افتح**: `test-audio.html` لاختبار نظام الأصوات
- **جرب جميع الأصوات**: والموسيقى قبل اللعب
- **تحكم في مستوى الصوت**: حسب تفضيلك

## 🛠️ المتطلبات التقنية

- **متصفح حديث** يدعم HTML5 و CSS3
- **JavaScript مفعل**
- **دقة شاشة**: 1200x600 أو أكبر (مُحسَّن للشاشات الكبيرة)

## 🎨 المميزات التقنية

### CSS المتقدم
- **Animations معقدة** لجميع الشخصيات
- **Gradients وتأثيرات** بصرية
- **Responsive design** للشاشات المختلفة
- **CSS Grid وFlexbox** للتخطيط

### JavaScript المتطور
- **نمط OOP** مع classes
- **محرك فيزياء** مخصص
- **إدارة حالة** متقدمة
- **نظام أحداث** شامل

### الأداء
- **تحسين الرسم** مع Canvas
- **إدارة ذاكرة** فعالة
- **تحميل تدريجي** للموارد

## 🔧 التخصيص

يمكنك تخصيص اللعبة بسهولة:

### إضافة مراحل جديدة
```javascript
class Level6 extends Level {
    constructor() {
        super("المرحلة السادسة", "space", 500);
        // تصميم المرحلة هنا
    }
}
```

### إضافة أعداء جدد
```javascript
class NewEnemy extends Enemy {
    constructor(x, y) {
        super(x, y, 32, 32);
        // منطق العدو الجديد
    }
}
```

### تعديل الألوان والتصاميم
عدّل ملفات CSS في مجلد `styles/`

## 🐛 استكشاف الأخطاء

### اللعبة لا تعمل؟
1. تأكد من تشغيل الملف من خادم ويب (وليس file://)
2. افتح Developer Tools وتحقق من الأخطاء
3. تأكد من وجود جميع الملفات

### الصوت لا يعمل؟
1. تحقق من وجود ملفات الصوت في مجلد `sounds/`
2. تأكد من أن المتصفح يدعم تشغيل الصوت
3. اقرأ `sounds/README.md` للمزيد

## 📝 الترخيص

هذا المشروع مفتوح المصدر ومتاح للاستخدام التعليمي والشخصي.

## 🤝 المساهمة

نرحب بالمساهمات! يمكنك:
- إضافة مراحل جديدة
- تحسين التصميم
- إصلاح الأخطاء
- إضافة مميزات جديدة

## 📞 الدعم

إذا واجهت أي مشاكل أو لديك اقتراحات، لا تتردد في التواصل!

---

**استمتع باللعب! 🎮✨**
